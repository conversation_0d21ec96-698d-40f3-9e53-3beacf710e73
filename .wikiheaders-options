projectfullname = SDL_ttf
projectshortname = SDL_ttf
incsubdir = include/SDL3_ttf
wikisubdir = SDL3_ttf
apiprefixregex = TTF_
mainincludefname = SDL3_ttf/SDL_ttf.h
versionfname = include/SDL3_ttf/SDL_ttf.h
versionmajorregex = \A\#define\s+SDL_TTF_MAJOR_VERSION\s+(\d+)\Z
versionminorregex = \A\#define\s+SDL_TTF_MINOR_VERSION\s+(\d+)\Z
versionmicroregex = \A\#define\s+SDL_TTF_MICRO_VERSION\s+(\d+)\Z
selectheaderregex = \ASDL_ttf\.h\Z
projecturl = https://libsdl.org/projects/SDL_ttf
wikiurl = https://wiki.libsdl.org/SDL_ttf
bugreporturl = https://github.com/libsdl-org/sdlwiki/issues/new
warn_about_missing = 0
wikipreamble = (This function is part of SDL_ttf, a separate library from SDL.)
wikiheaderfiletext = Defined in [<SDL3_ttf/%fname%>](https://github.com/libsdl-org/SDL_ttf/blob/main/include/SDL3_ttf/%fname%)
manpageheaderfiletext = Defined in SDL3_ttf/%fname%
quickrefenabled = 1
quickreftitle = SDL3_ttf API Quick Reference
quickrefurl = https://libsdl.org/
quickrefdesc = The latest version of this document can be found at https://wiki.libsdl.org/SDL3_ttf/QuickReference
