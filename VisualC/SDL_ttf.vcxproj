﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <SolutionDir Condition="'$(SolutionDir)'==''">$(ProjectDir)</SolutionDir>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectName>SDL3_ttf</ProjectName>
    <ProjectGuid>{DDDBD07D-DC76-4AF6-8D02-3E2DEB6EE255}</ProjectGuid>
    <RootNamespace>SDL_ttf</RootNamespace>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <PlatformToolset Condition="'$(VisualStudioVersion)' != '10.0'">$(DefaultPlatformToolset)</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <PlatformToolset Condition="'$(VisualStudioVersion)' != '10.0'">$(DefaultPlatformToolset)</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <PlatformToolset Condition="'$(VisualStudioVersion)' != '10.0'">$(DefaultPlatformToolset)</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <PlatformToolset Condition="'$(VisualStudioVersion)' != '10.0'">$(DefaultPlatformToolset)</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC60.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC60.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC60.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC60.props" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.40219.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">$(SolutionDir)\$(Platform)\$(Configuration)\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">$(Platform)\$(Configuration)\</IntDir>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(SolutionDir)\$(Platform)\$(Configuration)\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\</IntDir>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">$(SolutionDir)\$(Platform)\$(Configuration)\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">$(Platform)\$(Configuration)\</IntDir>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(SolutionDir)\$(Platform)\$(Configuration)\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(Platform)\$(Configuration)\</IntDir>
    <CodeAnalysisRuleSet Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">AllRules.ruleset</CodeAnalysisRuleSet>
    <CodeAnalysisRules Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" />
    <CodeAnalysisRuleAssemblies Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" />
    <CodeAnalysisRuleSet Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">AllRules.ruleset</CodeAnalysisRuleSet>
    <CodeAnalysisRules Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" />
    <CodeAnalysisRuleAssemblies Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" />
    <CodeAnalysisRuleSet Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">AllRules.ruleset</CodeAnalysisRuleSet>
    <CodeAnalysisRules Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" />
    <CodeAnalysisRuleAssemblies Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" />
    <CodeAnalysisRuleSet Condition="'$(Configuration)|$(Platform)'=='Release|x64'">AllRules.ruleset</CodeAnalysisRuleSet>
    <CodeAnalysisRules Condition="'$(Configuration)|$(Platform)'=='Release|x64'" />
    <CodeAnalysisRuleAssemblies Condition="'$(Configuration)|$(Platform)'=='Release|x64'" />
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <IncludePath>$(ProjectDir)..\include;$(ProjectDir)..\external\freetype\include;$(ProjectDir)..\external\harfbuzz;$(ProjectDir)..\external\harfbuzz\src;$(ProjectDir)..\external\plutosvg\source;$(ProjectDir)..\external\plutovg\include;$(ProjectDir)..\..\SDL\include;$(IncludePath)</IncludePath>
    <LibraryPath>$(SolutionDir)..\..\SDL\VisualC\$(PlatformName)\$(Configuration);$(LibraryPath)</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <IncludePath>$(ProjectDir)..\include;$(ProjectDir)..\external\freetype\include;$(ProjectDir)..\external\harfbuzz;$(ProjectDir)..\external\harfbuzz\src;$(ProjectDir)..\external\plutosvg\source;$(ProjectDir)..\external\plutovg\include;$(ProjectDir)..\..\SDL\include;$(IncludePath)</IncludePath>
    <LibraryPath>$(SolutionDir)..\..\SDL\VisualC\$(PlatformName)\$(Configuration);$(LibraryPath)</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <IncludePath>$(ProjectDir)..\include;$(ProjectDir)..\external\freetype\include;$(ProjectDir)..\external\harfbuzz;$(ProjectDir)..\external\harfbuzz\src;$(ProjectDir)..\external\plutosvg\source;$(ProjectDir)..\external\plutovg\include;$(ProjectDir)..\..\SDL\include;$(IncludePath)</IncludePath>
    <LibraryPath>$(SolutionDir)..\..\SDL\VisualC\$(PlatformName)\$(Configuration);$(LibraryPath)</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <IncludePath>$(ProjectDir)..\include;$(ProjectDir)..\external\freetype\include;$(ProjectDir)..\external\harfbuzz;$(ProjectDir)..\external\harfbuzz\src;$(ProjectDir)..\external\plutosvg\source;$(ProjectDir)..\external\plutovg\include;$(ProjectDir)..\..\SDL\include;$(IncludePath)</IncludePath>
    <LibraryPath>$(SolutionDir)..\..\SDL\VisualC\$(PlatformName)\$(Configuration);$(LibraryPath)</LibraryPath>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Midl>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TargetEnvironment>Win32</TargetEnvironment>
      <TypeLibraryName>./$(Configuration)/$(Platform)/SDL3_ttf.tlb</TypeLibraryName>
      <HeaderFileName>
      </HeaderFileName>
    </Midl>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>DLL_EXPORT;WIN32;_DEBUG;_WINDOWS;_CRT_SECURE_NO_WARNINGS;TTF_USE_HARFBUZZ=1;HAVE_CONFIG_H;FT_PUBLIC_FUNCTION_ATTRIBUTE=;FT_CONFIG_OPTION_USE_HARFBUZZ;FT2_BUILD_LIBRARY;HAVE_FREETYPE;TTF_USE_PLUTOSVG=1;PLUTOSVG_HAS_FREETYPE;PLUTOSVG_BUILD_STATIC;PLUTOVG_BUILD_STATIC;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>OldStyle</DebugInformationFormat>
      <EnableEnhancedInstructionSet>StreamingSIMDExtensions</EnableEnhancedInstructionSet>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
    <Link>
      <AdditionalDependencies>usp10.lib;gdi32.lib;rpcrt4.lib;SDL3.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>external\lib\x86;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Windows</SubSystem>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TargetEnvironment>X64</TargetEnvironment>
      <TypeLibraryName>./$(Configuration)/$(Platform)/SDL3_ttf.tlb</TypeLibraryName>
      <HeaderFileName>
      </HeaderFileName>
    </Midl>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>DLL_EXPORT;WIN32;_DEBUG;_WINDOWS;_CRT_SECURE_NO_WARNINGS;TTF_USE_HARFBUZZ=1;HAVE_CONFIG_H;FT_PUBLIC_FUNCTION_ATTRIBUTE=;FT_CONFIG_OPTION_USE_HARFBUZZ;FT2_BUILD_LIBRARY;HAVE_FREETYPE;TTF_USE_PLUTOSVG=1;PLUTOSVG_HAS_FREETYPE;PLUTOSVG_BUILD_STATIC;PLUTOVG_BUILD_STATIC;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>OldStyle</DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
    <Link>
      <AdditionalDependencies>usp10.lib;gdi32.lib;rpcrt4.lib;SDL3.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>external\lib\x64;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Windows</SubSystem>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Midl>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TargetEnvironment>Win32</TargetEnvironment>
      <TypeLibraryName>./$(Configuration)/$(Platform)/SDL3_ttf.tlb</TypeLibraryName>
      <HeaderFileName>
      </HeaderFileName>
    </Midl>
    <ClCompile>
      <PreprocessorDefinitions>DLL_EXPORT;WIN32;NDEBUG;_WINDOWS;_CRT_SECURE_NO_WARNINGS;TTF_USE_HARFBUZZ=1;HAVE_CONFIG_H;FT_PUBLIC_FUNCTION_ATTRIBUTE=;FT_CONFIG_OPTION_USE_HARFBUZZ;FT2_BUILD_LIBRARY;HAVE_FREETYPE;TTF_USE_PLUTOSVG=1;PLUTOSVG_HAS_FREETYPE;PLUTOSVG_BUILD_STATIC;PLUTOVG_BUILD_STATIC;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <WarningLevel>Level3</WarningLevel>
      <EnableEnhancedInstructionSet>StreamingSIMDExtensions</EnableEnhancedInstructionSet>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <AdditionalDependencies>usp10.lib;gdi32.lib;rpcrt4.lib;SDL3.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>external\lib\x86;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Midl>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TargetEnvironment>X64</TargetEnvironment>
      <TypeLibraryName>./$(Configuration)/$(Platform)/SDL3_ttf.tlb</TypeLibraryName>
      <HeaderFileName>
      </HeaderFileName>
    </Midl>
    <ClCompile>
      <PreprocessorDefinitions>DLL_EXPORT;WIN32;NDEBUG;_WINDOWS;_CRT_SECURE_NO_WARNINGS;TTF_USE_HARFBUZZ=1;HAVE_CONFIG_H;FT_PUBLIC_FUNCTION_ATTRIBUTE=;FT_CONFIG_OPTION_USE_HARFBUZZ;FT2_BUILD_LIBRARY;HAVE_FREETYPE;TTF_USE_PLUTOSVG=1;PLUTOSVG_HAS_FREETYPE;PLUTOSVG_BUILD_STATIC;PLUTOVG_BUILD_STATIC;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <WarningLevel>Level3</WarningLevel>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <AdditionalDependencies>usp10.lib;gdi32.lib;rpcrt4.lib;SDL3.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>external\lib\x64;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="..\external\freetype\src\autofit\autofit.c" />
    <ClCompile Include="..\external\freetype\src\base\ftbase.c" />
    <ClCompile Include="..\external\freetype\src\base\ftbbox.c" />
    <ClCompile Include="..\external\freetype\src\base\ftbdf.c" />
    <ClCompile Include="..\external\freetype\src\base\ftbitmap.c" />
    <ClCompile Include="..\external\freetype\src\base\ftcid.c" />
    <ClCompile Include="..\external\freetype\src\base\ftdebug.c" />
    <ClCompile Include="..\external\freetype\src\base\ftfstype.c" />
    <ClCompile Include="..\external\freetype\src\base\ftgasp.c" />
    <ClCompile Include="..\external\freetype\src\base\ftglyph.c" />
    <ClCompile Include="..\external\freetype\src\base\ftgxval.c" />
    <ClCompile Include="..\external\freetype\src\base\ftinit.c" />
    <ClCompile Include="..\external\freetype\src\base\ftmm.c" />
    <ClCompile Include="..\external\freetype\src\base\ftotval.c" />
    <ClCompile Include="..\external\freetype\src\base\ftpatent.c" />
    <ClCompile Include="..\external\freetype\src\base\ftpfr.c" />
    <ClCompile Include="..\external\freetype\src\base\ftstroke.c" />
    <ClCompile Include="..\external\freetype\src\base\ftsynth.c" />
    <ClCompile Include="..\external\freetype\src\base\ftsystem.c" />
    <ClCompile Include="..\external\freetype\src\base\fttype1.c" />
    <ClCompile Include="..\external\freetype\src\base\ftwinfnt.c" />
    <ClCompile Include="..\external\freetype\src\bdf\bdf.c" />
    <ClCompile Include="..\external\freetype\src\cache\ftcache.c" />
    <ClCompile Include="..\external\freetype\src\cff\cff.c" />
    <ClCompile Include="..\external\freetype\src\cid\type1cid.c" />
    <ClCompile Include="..\external\freetype\src\gzip\ftgzip.c" />
    <ClCompile Include="..\external\freetype\src\lzw\ftlzw.c" />
    <ClCompile Include="..\external\freetype\src\pcf\pcf.c" />
    <ClCompile Include="..\external\freetype\src\pfr\pfr.c" />
    <ClCompile Include="..\external\freetype\src\psaux\psaux.c" />
    <ClCompile Include="..\external\freetype\src\pshinter\pshinter.c" />
    <ClCompile Include="..\external\freetype\src\psnames\psmodule.c" />
    <ClCompile Include="..\external\freetype\src\raster\raster.c" />
    <ClCompile Include="..\external\freetype\src\sdf\sdf.c" />
    <ClCompile Include="..\external\freetype\src\sfnt\sfnt.c" />
    <ClCompile Include="..\external\freetype\src\svg\svg.c" />
    <ClCompile Include="..\external\freetype\src\smooth\smooth.c" />
    <ClCompile Include="..\external\freetype\src\truetype\truetype.c" />
    <ClCompile Include="..\external\freetype\src\type1\type1.c" />
    <ClCompile Include="..\external\freetype\src\type42\type42.c" />
    <ClCompile Include="..\external\freetype\src\winfonts\winfnt.c" />
    <ClCompile Include="..\external\harfbuzz\src\harfbuzz.cc" />
    <ClCompile Include="..\external\plutosvg\source\plutosvg.c" />
    <ClCompile Include="..\external\plutovg\source\plutovg-blend.c" />
    <ClCompile Include="..\external\plutovg\source\plutovg-canvas.c" />
    <ClCompile Include="..\external\plutovg\source\plutovg-font.c" />
    <ClCompile Include="..\external\plutovg\source\plutovg-ft-math.c" />
    <ClCompile Include="..\external\plutovg\source\plutovg-ft-raster.c" />
    <ClCompile Include="..\external\plutovg\source\plutovg-ft-stroker.c" />
    <ClCompile Include="..\external\plutovg\source\plutovg-matrix.c" />
    <ClCompile Include="..\external\plutovg\source\plutovg-paint.c" />
    <ClCompile Include="..\external\plutovg\source\plutovg-path.c" />
    <ClCompile Include="..\external\plutovg\source\plutovg-rasterize.c" />
    <ClCompile Include="..\external\plutovg\source\plutovg-surface.c" />
    <ClCompile Include="..\src\SDL_gpu_textengine.c" />
    <ClCompile Include="..\src\SDL_hashtable.c" />
    <ClCompile Include="..\src\SDL_hashtable_ttf.c" />
    <ClCompile Include="..\src\SDL_renderer_textengine.c" />
    <ClCompile Include="..\src\SDL_surface_textengine.c" />
    <ClCompile Include="..\src\SDL_ttf.c" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="..\src\version.rc" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\external\plutosvg\source\plutosvg.h" />
    <ClInclude Include="..\external\plutovg\source\plutovg-ft-math.h" />
    <ClInclude Include="..\external\plutovg\source\plutovg-ft-raster.h" />
    <ClInclude Include="..\external\plutovg\source\plutovg-ft-stroker.h" />
    <ClInclude Include="..\external\plutovg\source\plutovg-ft-types.h" />
    <ClInclude Include="..\external\plutovg\source\plutovg-private.h" />
    <ClInclude Include="..\external\plutovg\source\plutovg-stb-image-write.h" />
    <ClInclude Include="..\external\plutovg\source\plutovg-stb-image.h" />
    <ClInclude Include="..\external\plutovg\source\plutovg-stb-truetype.h" />
    <ClInclude Include="..\external\plutovg\source\plutovg-utils.h" />
    <ClInclude Include="..\include\SDL3_ttf\SDL_textengine.h" />
    <ClInclude Include="..\include\SDL3_ttf\SDL_ttf.h" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>