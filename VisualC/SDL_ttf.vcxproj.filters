﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Sources">
      <UniqueIdentifier>{bcf4d3a9-19ff-48f0-b98c-d00107144bbf}</UniqueIdentifier>
    </Filter>
    <Filter Include="Public Headers">
      <UniqueIdentifier>{8c2c480d-888e-47ec-bec8-6e67eb40d529}</UniqueIdentifier>
    </Filter>
    <Filter Include="Sources\FreeType">
      <UniqueIdentifier>{b4bb09ca-097b-41dd-8645-72157253717a}</UniqueIdentifier>
    </Filter>
    <Filter Include="Sources\HarfBuzz">
      <UniqueIdentifier>{1adce3a3-3929-463e-9fc4-76aabd487471}</UniqueIdentifier>
    </Filter>
    <Filter Include="Sources\PlutoSVG">
      <UniqueIdentifier>{12b2531a-b0bd-49fb-a66e-8397c3e7dcee}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\src\SDL_hashtable.c">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="..\src\SDL_hashtable_ttf.c">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="..\src\SDL_renderer_textengine.c">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="..\src\SDL_surface_textengine.c">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="..\src\SDL_ttf.c">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\autofit\autofit.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\base\ftbase.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\base\ftbbox.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\base\ftbdf.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\base\ftbitmap.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\base\ftcid.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\base\ftdebug.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\base\ftfstype.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\base\ftgasp.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\base\ftglyph.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\base\ftgxval.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\base\ftinit.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\base\ftmm.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\base\ftotval.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\base\ftpatent.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\base\ftpfr.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\base\ftstroke.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\base\ftsynth.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\base\ftsystem.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\base\fttype1.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\base\ftwinfnt.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\bdf\bdf.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\cache\ftcache.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\cff\cff.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\cid\type1cid.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\gzip\ftgzip.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\lzw\ftlzw.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\pcf\pcf.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\pfr\pfr.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\psaux\psaux.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\pshinter\pshinter.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\psnames\psmodule.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\raster\raster.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\sdf\sdf.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\sfnt\sfnt.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\svg\svg.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\smooth\smooth.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\truetype\truetype.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\type1\type1.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\type42\type42.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\freetype\src\winfonts\winfnt.c">
      <Filter>Sources\FreeType</Filter>
    </ClCompile>
    <ClCompile Include="..\external\harfbuzz\src\harfbuzz.cc">
      <Filter>Sources\HarfBuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\src\SDL_gpu_textengine.c">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="..\external\plutosvg\source\plutosvg.c">
      <Filter>Sources\PlutoSVG</Filter>
    </ClCompile>
    <ClCompile Include="..\external\plutovg\source\plutovg-blend.c">
      <Filter>Sources\PlutoSVG</Filter>
    </ClCompile>
    <ClCompile Include="..\external\plutovg\source\plutovg-canvas.c">
      <Filter>Sources\PlutoSVG</Filter>
    </ClCompile>
    <ClCompile Include="..\external\plutovg\source\plutovg-font.c">
      <Filter>Sources\PlutoSVG</Filter>
    </ClCompile>
    <ClCompile Include="..\external\plutovg\source\plutovg-ft-math.c">
      <Filter>Sources\PlutoSVG</Filter>
    </ClCompile>
    <ClCompile Include="..\external\plutovg\source\plutovg-ft-raster.c">
      <Filter>Sources\PlutoSVG</Filter>
    </ClCompile>
    <ClCompile Include="..\external\plutovg\source\plutovg-ft-stroker.c">
      <Filter>Sources\PlutoSVG</Filter>
    </ClCompile>
    <ClCompile Include="..\external\plutovg\source\plutovg-matrix.c">
      <Filter>Sources\PlutoSVG</Filter>
    </ClCompile>
    <ClCompile Include="..\external\plutovg\source\plutovg-paint.c">
      <Filter>Sources\PlutoSVG</Filter>
    </ClCompile>
    <ClCompile Include="..\external\plutovg\source\plutovg-path.c">
      <Filter>Sources\PlutoSVG</Filter>
    </ClCompile>
    <ClCompile Include="..\external\plutovg\source\plutovg-rasterize.c">
      <Filter>Sources\PlutoSVG</Filter>
    </ClCompile>
    <ClCompile Include="..\external\plutovg\source\plutovg-surface.c">
      <Filter>Sources\PlutoSVG</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="..\src\version.rc">
      <Filter>Sources</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\include\SDL3_ttf\SDL_textengine.h">
      <Filter>Public Headers</Filter>
    </ClInclude>
    <ClInclude Include="..\include\SDL3_ttf\SDL_ttf.h">
      <Filter>Public Headers</Filter>
    </ClInclude>
    <ClInclude Include="..\external\plutosvg\source\plutosvg.h">
      <Filter>Sources\PlutoSVG</Filter>
    </ClInclude>
    <ClInclude Include="..\external\plutovg\source\plutovg-ft-math.h">
      <Filter>Sources\PlutoSVG</Filter>
    </ClInclude>
    <ClInclude Include="..\external\plutovg\source\plutovg-ft-raster.h">
      <Filter>Sources\PlutoSVG</Filter>
    </ClInclude>
    <ClInclude Include="..\external\plutovg\source\plutovg-ft-stroker.h">
      <Filter>Sources\PlutoSVG</Filter>
    </ClInclude>
    <ClInclude Include="..\external\plutovg\source\plutovg-ft-types.h">
      <Filter>Sources\PlutoSVG</Filter>
    </ClInclude>
    <ClInclude Include="..\external\plutovg\source\plutovg-private.h">
      <Filter>Sources\PlutoSVG</Filter>
    </ClInclude>
    <ClInclude Include="..\external\plutovg\source\plutovg-stb-image.h">
      <Filter>Sources\PlutoSVG</Filter>
    </ClInclude>
    <ClInclude Include="..\external\plutovg\source\plutovg-stb-image-write.h">
      <Filter>Sources\PlutoSVG</Filter>
    </ClInclude>
    <ClInclude Include="..\external\plutovg\source\plutovg-stb-truetype.h">
      <Filter>Sources\PlutoSVG</Filter>
    </ClInclude>
    <ClInclude Include="..\external\plutovg\source\plutovg-utils.h">
      <Filter>Sources\PlutoSVG</Filter>
    </ClInclude>
  </ItemGroup>
</Project>
