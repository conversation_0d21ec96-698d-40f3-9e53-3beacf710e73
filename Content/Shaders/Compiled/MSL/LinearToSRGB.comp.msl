#include <metal_stdlib>
#include <simd/simd.h>

using namespace metal;

kernel void main0(texture2d<float> InImage [[texture(0)]], texture2d<float, access::write> OutImage [[texture(1)]], uint3 gl_GlobalInvocationID [[thread_position_in_grid]])
{
    uint2 _29 = uint2(int2(gl_GlobalInvocationID.xy));
    OutImage.write(float4(powr(abs(InImage.read(uint2(_29), 0u).xyz), float3(0.454545438289642333984375)), 1.0), uint2(_29));
}

