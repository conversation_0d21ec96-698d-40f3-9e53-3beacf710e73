#include <metal_stdlib>
#include <simd/simd.h>

using namespace metal;

struct type_UniformBlock
{
    float4x4 MatrixTransform;
};

struct main0_out
{
    float3 out_var_TEXCOORD0 [[user(locn0)]];
    float4 gl_Position [[position]];
};

struct main0_in
{
    float3 in_var_TEXCOORD0 [[attribute(0)]];
};

vertex main0_out main0(main0_in in [[stage_in]], constant type_UniformBlock& UniformBlock [[buffer(0)]])
{
    main0_out out = {};
    out.out_var_TEXCOORD0 = in.in_var_TEXCOORD0;
    out.gl_Position = UniformBlock.MatrixTransform * float4(in.in_var_TEXCOORD0, 1.0);
    return out;
}

