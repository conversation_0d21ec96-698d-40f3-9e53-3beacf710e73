#pragma clang diagnostic ignored "-Wmissing-prototypes"
#pragma clang diagnostic ignored "-Wmissing-braces"

#include <metal_stdlib>
#include <simd/simd.h>

using namespace metal;

template<typename T, size_t Num>
struct spvUnsafeArray
{
    T elements[Num ? Num : 1];
    
    thread T& operator [] (size_t pos) thread
    {
        return elements[pos];
    }
    constexpr const thread T& operator [] (size_t pos) const thread
    {
        return elements[pos];
    }
    
    device T& operator [] (size_t pos) device
    {
        return elements[pos];
    }
    constexpr const device T& operator [] (size_t pos) const device
    {
        return elements[pos];
    }
    
    constexpr const constant T& operator [] (size_t pos) const constant
    {
        return elements[pos];
    }
    
    threadgroup T& operator [] (size_t pos) threadgroup
    {
        return elements[pos];
    }
    constexpr const threadgroup T& operator [] (size_t pos) const threadgroup
    {
        return elements[pos];
    }
};

struct SpriteData
{
    packed_float3 Position;
    float Rotation;
    float2 Scale;
    float2 Padding;
    float TexU;
    float TexV;
    float TexW;
    float TexH;
    float4 Color;
};

struct type_StructuredBuffer_SpriteData
{
    SpriteData _m0[1];
};

struct type_UniformBlock
{
    float4x4 ViewProjectionMatrix;
};

constant spvUnsafeArray<uint, 6> _46 = spvUnsafeArray<uint, 6>({ 0u, 1u, 2u, 3u, 2u, 1u });
constant spvUnsafeArray<float2, 4> _51 = spvUnsafeArray<float2, 4>({ float2(0.0), float2(1.0, 0.0), float2(0.0, 1.0), float2(1.0) });

struct main0_out
{
    float2 out_var_TEXCOORD0 [[user(locn0)]];
    float4 out_var_TEXCOORD1 [[user(locn1)]];
    float4 gl_Position [[position]];
};

vertex main0_out main0(constant type_UniformBlock& UniformBlock [[buffer(0)]], const device type_StructuredBuffer_SpriteData& DataBuffer [[buffer(1)]], uint gl_VertexIndex [[vertex_id]])
{
    main0_out out = {};
    uint _62 = gl_VertexIndex / 6u;
    uint _63 = gl_VertexIndex % 6u;
    float _82 = DataBuffer._m0[_62].TexU + DataBuffer._m0[_62].TexW;
    float _83 = DataBuffer._m0[_62].TexV + DataBuffer._m0[_62].TexH;
    spvUnsafeArray<float2, 4> _88 = spvUnsafeArray<float2, 4>({ float2(DataBuffer._m0[_62].TexU, DataBuffer._m0[_62].TexV), float2(_82, DataBuffer._m0[_62].TexV), float2(DataBuffer._m0[_62].TexU, _83), float2(_82, _83) });
    spvUnsafeArray<float2, 4> _60 = _88;
    float _89 = cos(DataBuffer._m0[_62].Rotation);
    float _90 = sin(DataBuffer._m0[_62].Rotation);
    out.out_var_TEXCOORD0 = _60[_46[_63]];
    out.out_var_TEXCOORD1 = DataBuffer._m0[_62].Color;
    out.gl_Position = UniformBlock.ViewProjectionMatrix * float4((float2x2(float2(_89, _90), float2(-_90, _89)) * (_51[_46[_63]] * DataBuffer._m0[_62].Scale)) + float2(DataBuffer._m0[_62].Position[0], DataBuffer._m0[_62].Position[1]), DataBuffer._m0[_62].Position[2], 1.0);
    return out;
}

