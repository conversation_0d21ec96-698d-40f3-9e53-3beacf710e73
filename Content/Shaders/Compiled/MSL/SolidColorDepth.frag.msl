#include <metal_stdlib>
#include <simd/simd.h>

using namespace metal;

struct type_UBO
{
    float NearPlane;
    float FarPlane;
};

struct main0_out
{
    float4 out_var_SV_Target0 [[color(0)]];
    float gl_FragDepth [[depth(any)]];
};

struct main0_in
{
    float4 in_var_TEXCOORD0 [[user(locn0)]];
};

fragment main0_out main0(main0_in in [[stage_in]], constant type_UBO& UBO [[buffer(0)]], float4 gl_FragCoord [[position]])
{
    main0_out out = {};
    out.out_var_SV_Target0 = in.in_var_TEXCOORD0;
    out.gl_FragDepth = (((2.0 * UBO.NearPlane) * UBO.FarPlane) / ((UBO.FarPlane + UBO.NearPlane) - (((gl_FragCoord.z * 2.0) - 1.0) * (UBO.FarPlane - UBO.NearPlane)))) / UBO.FarPlane;
    return out;
}

