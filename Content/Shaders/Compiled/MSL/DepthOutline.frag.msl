#include <metal_stdlib>
#include <simd/simd.h>

using namespace metal;

struct main0_out
{
    float4 out_var_SV_Target0 [[color(0)]];
};

struct main0_in
{
    float2 in_var_TEXCOORD0 [[user(locn0)]];
};

fragment main0_out main0(main0_in in [[stage_in]], texture2d<float> ColorTexture [[texture(0)]], texture2d<float> DepthTexture [[texture(1)]], sampler ColorSampler [[sampler(0)]], sampler DepthSampler [[sampler(1)]])
{
    main0_out out = {};
    float4 _38 = ColorTexture.sample(ColorSampler, in.in_var_TEXCOORD0);
    float4 _42 = DepthTexture.sample(DepthSampler, in.in_var_TEXCOORD0);
    float _43 = _42.x;
    uint2 _45 = uint2(DepthTexture.get_width(), DepthTexture.get_height());
    float _47 = float(_45.x);
    float _49 = float(_45.y);
    uint2 _95 = uint2(DepthTexture.get_width(), DepthTexture.get_height());
    float _97 = float(_95.x);
    float _99 = float(_95.y);
    float3 _148 = mix(mix(_38.xyz, float3(0.0), float3(step(0.20000000298023223876953125, precise::max(DepthTexture.sample(DepthSampler, (in.in_var_TEXCOORD0 + (float2(1.0 / _97, 0.0) * 2.0))).x - _43, precise::max(DepthTexture.sample(DepthSampler, (in.in_var_TEXCOORD0 + (float2((-1.0) / _97, 0.0) * 2.0))).x - _43, precise::max(DepthTexture.sample(DepthSampler, (in.in_var_TEXCOORD0 + (float2(0.0, 1.0 / _99) * 2.0))).x - _43, DepthTexture.sample(DepthSampler, (in.in_var_TEXCOORD0 + (float2(0.0, (-1.0) / _99) * 2.0))).x - _43)))))), float3(1.0), float3(step(0.20000000298023223876953125, precise::max(DepthTexture.sample(DepthSampler, (in.in_var_TEXCOORD0 + (float2(1.0 / _47, 0.0) * 1.0))).x - _43, precise::max(DepthTexture.sample(DepthSampler, (in.in_var_TEXCOORD0 + (float2((-1.0) / _47, 0.0) * 1.0))).x - _43, precise::max(DepthTexture.sample(DepthSampler, (in.in_var_TEXCOORD0 + (float2(0.0, 1.0 / _49) * 1.0))).x - _43, DepthTexture.sample(DepthSampler, (in.in_var_TEXCOORD0 + (float2(0.0, (-1.0) / _49) * 1.0))).x - _43))))));
    out.out_var_SV_Target0 = float4(_148, _38.w);
    return out;
}

