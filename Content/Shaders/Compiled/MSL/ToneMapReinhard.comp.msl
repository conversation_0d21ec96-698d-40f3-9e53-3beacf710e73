#include <metal_stdlib>
#include <simd/simd.h>

using namespace metal;

kernel void main0(texture2d<float> inImage [[texture(0)]], texture2d<float, access::write> outImage [[texture(1)]], uint3 gl_GlobalInvocationID [[thread_position_in_grid]])
{
    uint2 _27 = uint2(int2(gl_GlobalInvocationID.xy));
    float3 _30 = inImage.read(uint2(_27), 0u).xyz;
    outImage.write(float4(_30 / (float3(1.0) + _30), 1.0), uint2(_27));
}

