#include <metal_stdlib>
#include <simd/simd.h>

using namespace metal;

struct main0_out
{
    float4 out_var_TEXCOORD0 [[user(locn0)]];
    float4 gl_Position [[position]];
};

struct main0_in
{
    float3 in_var_TEXCOORD0 [[attribute(0)]];
    float4 in_var_TEXCOORD1 [[attribute(1)]];
};

vertex main0_out main0(main0_in in [[stage_in]], uint gl_InstanceIndex [[instance_id]])
{
    main0_out out = {};
    float3 _30 = (in.in_var_TEXCOORD0 * 0.25) - float3(0.75, 0.75, 0.0);
    out.out_var_TEXCOORD0 = in.in_var_TEXCOORD1;
    out.gl_Position = float4(_30.x + (float(gl_InstanceIndex % 4u) * 0.5), _30.y + (floor(float(gl_InstanceIndex / 4u)) * 0.5), _30.z, 1.0);
    return out;
}

