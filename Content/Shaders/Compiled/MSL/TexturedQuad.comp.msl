#include <metal_stdlib>
#include <simd/simd.h>

using namespace metal;

struct type_UBO
{
    float ubo_texcoord_multiplier;
};

kernel void main0(constant type_UBO& UBO [[buffer(0)]], texture2d<float> inImage [[texture(0)]], texture2d<float, access::write> outImage [[texture(1)]], sampler inImageSampler [[sampler(0)]], uint3 gl_GlobalInvocationID [[thread_position_in_grid]])
{
    uint2 _33 = uint2(inImage.get_width(), inImage.get_height());
    int2 _39 = int2(gl_GlobalInvocationID.xy);
    outImage.write(inImage.sample(inImageSampler, ((float2(_39) * UBO.ubo_texcoord_multiplier) / float2(float(_33.x), float(_33.y))), level(0.0)), uint2(uint2(_39)));
}

