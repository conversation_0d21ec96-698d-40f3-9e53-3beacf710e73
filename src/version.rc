
#include "winresrc.h"

LANGUAGE LANG_ENGLISH, SUBLANG_ENGLISH_US

/////////////////////////////////////////////////////////////////////////////
//
// Version
//

VS_VERSION_INFO VERSIONINFO
 FILEVERSION 3,3,0,0
 PRODUCTVERSION 3,3,0,0
 FILEFLAGSMASK 0x3fL
 FILEFLAGS 0x0L
 FILEOS 0x40004L
 FILETYPE 0x2L
 FILESUBTYPE 0x0L
BEGIN
    BLOCK "StringFileInfo"
    BEGIN
        BLOCK "040904b0"
        BEGIN
            VALUE "CompanyName", "\0"
            VALUE "FileDescription", "SDL_ttf\0"
            VALUE "FileVersion", "3, 3, 0, 0\0"
            VALUE "InternalName", "SDL_ttf\0"
            VALUE "LegalCopyright", "Copyright (C) 2025 Sam <PERSON>\0"
            VALUE "OriginalFilename", "SDL3_ttf.dll\0"
            VALUE "ProductName", "Simple DirectMedia Layer\0"
            VALUE "ProductVersion", "3, 3, 0, 0\0"
        END
    END
    BLOCK "VarFileInfo"
    BEGIN
        VALUE "Translation", 0x409, 1200
    END
END
