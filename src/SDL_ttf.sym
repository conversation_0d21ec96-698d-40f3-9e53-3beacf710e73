SDL3_ttf_0.0.0 {
  global:
    TTF_AddFallbackFont;
    TTF_AppendTextString;
    TTF_ClearFallbackFonts;
    TTF_CloseFont;
    TTF_CopyFont;
    TTF_CreateGPUTextEngine;
    TTF_CreateGPUTextEngineWithProperties;
    TTF_CreateRendererTextEngine;
    TTF_CreateRendererTextEngineWithProperties;
    TTF_CreateSurfaceTextEngine;
    TTF_CreateText;
    TTF_DeleteTextString;
    TTF_DestroyGPUTextEngine;
    TTF_DestroyRendererTextEngine;
    TTF_DestroySurfaceTextEngine;
    TTF_DestroyText;
    TTF_DrawRendererText;
    TTF_DrawSurfaceText;
    TTF_FontHasGlyph;
    TTF_FontIsFixedWidth;
    TTF_FontIsScalable;
    TTF_GetFontAscent;
    TTF_GetFontDPI;
    TTF_GetFontDescent;
    TTF_GetFontDirection;
    TTF_GetFontFamilyName;
    TTF_GetFontGeneration;
    TTF_GetFontHeight;
    TTF_GetFontHinting;
    TTF_GetFontKerning;
    TTF_GetFontLineSkip;
    TTF_GetFontOutline;
    TTF_GetFontProperties;
    TTF_GetFontScript;
    TTF_GetFontSDF;
    TTF_GetFontSize;
    TTF_GetFontStyle;
    TTF_GetFontStyleName;
    TTF_GetFontWeight;
    TTF_GetFontWrapAlignment;
    TTF_GetFreeTypeVersion;
    TTF_GetGlyphImage;
    TTF_GetGlyphImageForIndex;
    TTF_GetGlyphKerning;
    TTF_GetGlyphMetrics;
    TTF_GetGlyphScript;
    TTF_GetGPUTextDrawData;
    TTF_GetGPUTextEngineWinding;
    TTF_GetHarfBuzzVersion;
    TTF_GetNextTextSubString;
    TTF_GetNumFontFaces;
    TTF_GetPreviousTextSubString;
    TTF_GetStringSize;
    TTF_GetStringSizeWrapped;
    TTF_GetTextColor;
    TTF_GetTextColorFloat;
    TTF_GetTextDirection;
    TTF_GetTextEngine;
    TTF_GetTextFont;
    TTF_GetTextPosition;
    TTF_GetTextProperties;
    TTF_GetTextScript;
    TTF_GetTextSize;
    TTF_GetTextSubString;
    TTF_GetTextSubStringForLine;
    TTF_GetTextSubStringForPoint;
    TTF_GetTextSubStringsForRange;
    TTF_GetTextWrapWidth;
    TTF_Init;
    TTF_InsertTextString;
    TTF_MeasureString;
    TTF_OpenFont;
    TTF_OpenFontIO;
    TTF_OpenFontWithProperties;
    TTF_Quit;
    TTF_RemoveFallbackFont;
    TTF_RenderGlyph_Blended;
    TTF_RenderGlyph_LCD;
    TTF_RenderGlyph_Shaded;
    TTF_RenderGlyph_Solid;
    TTF_RenderText_Blended;
    TTF_RenderText_Blended_Wrapped;
    TTF_RenderText_LCD;
    TTF_RenderText_LCD_Wrapped;
    TTF_RenderText_Shaded;
    TTF_RenderText_Shaded_Wrapped;
    TTF_RenderText_Solid;
    TTF_RenderText_Solid_Wrapped;
    TTF_SetFontDirection;
    TTF_SetFontHinting;
    TTF_SetFontKerning;
    TTF_SetFontLanguage;
    TTF_SetFontLineSkip;
    TTF_SetFontOutline;
    TTF_SetFontSDF;
    TTF_SetFontScript;
    TTF_SetFontSize;
    TTF_SetFontSizeDPI;
    TTF_SetFontStyle;
    TTF_SetFontWrapAlignment;
    TTF_SetGPUTextEngineWinding;
    TTF_SetTextColor;
    TTF_SetTextColorFloat;
    TTF_SetTextDirection;
    TTF_SetTextEngine;
    TTF_SetTextFont;
    TTF_SetTextPosition;
    TTF_SetTextScript;
    TTF_SetTextString;
    TTF_SetTextWrapWhitespaceVisible;
    TTF_SetTextWrapWidth;
    TTF_StringToTag;
    TTF_TagToString;
    TTF_TextWrapWhitespaceVisible;
    TTF_UpdateText;
    TTF_Version;
    TTF_WasInit;
  local: *;
};
