static const unsigned char shader_sdf_frag_dxil[] = {
  0x44, 0x58, 0x42, 0x43, 0xac, 0x2e, 0xf5, 0x28, 0x61, 0xcf, 0x6c, 0xaa,
  0xb4, 0xaa, 0xd1, 0x6c, 0x85, 0xed, 0x95, 0xe3, 0x01, 0x00, 0x00, 0x00,
  0x84, 0x0f, 0x00, 0x00, 0x07, 0x00, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x00,
  0x4c, 0x00, 0x00, 0x00, 0xa8, 0x00, 0x00, 0x00, 0xe4, 0x00, 0x00, 0x00,
  0xd8, 0x01, 0x00, 0x00, 0x4c, 0x08, 0x00, 0x00, 0x68, 0x08, 0x00, 0x00,
  0x53, 0x46, 0x49, 0x30, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x49, 0x53, 0x47, 0x31, 0x54, 0x00, 0x00, 0x00,
  0x02, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x48, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0f, 0x0f, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x48, 0x00, 0x00, 0x00,
  0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00,
  0x01, 0x00, 0x00, 0x00, 0x03, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x54, 0x45, 0x58, 0x43, 0x4f, 0x4f, 0x52, 0x44, 0x00, 0x00, 0x00, 0x00,
  0x4f, 0x53, 0x47, 0x31, 0x34, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00,
  0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x28, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x0f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x53, 0x56, 0x5f, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x00, 0x00, 0x00,
  0x50, 0x53, 0x56, 0x30, 0xec, 0x00, 0x00, 0x00, 0x34, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff,
  0x00, 0x00, 0x00, 0x00, 0x02, 0x01, 0x00, 0x02, 0x01, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x13, 0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00,
  0x01, 0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x0e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x03, 0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x18, 0x00, 0x00, 0x00, 0x00, 0x54, 0x45, 0x58, 0x43, 0x4f, 0x4f, 0x52,
  0x44, 0x00, 0x54, 0x45, 0x58, 0x43, 0x4f, 0x4f, 0x52, 0x44, 0x00, 0x6d,
  0x61, 0x69, 0x6e, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x01, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x44, 0x00, 0x03, 0x02, 0x00, 0x00,
  0x0a, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01, 0x01, 0x42, 0x00,
  0x03, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x01, 0x00, 0x44, 0x10, 0x03, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00,
  0x02, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00,
  0x08, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x53, 0x54, 0x41, 0x54, 0x6c, 0x06, 0x00, 0x00,
  0x60, 0x00, 0x00, 0x00, 0x9b, 0x01, 0x00, 0x00, 0x44, 0x58, 0x49, 0x4c,
  0x00, 0x01, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x54, 0x06, 0x00, 0x00,
  0x42, 0x43, 0xc0, 0xde, 0x21, 0x0c, 0x00, 0x00, 0x92, 0x01, 0x00, 0x00,
  0x0b, 0x82, 0x20, 0x00, 0x02, 0x00, 0x00, 0x00, 0x13, 0x00, 0x00, 0x00,
  0x07, 0x81, 0x23, 0x91, 0x41, 0xc8, 0x04, 0x49, 0x06, 0x10, 0x32, 0x39,
  0x92, 0x01, 0x84, 0x0c, 0x25, 0x05, 0x08, 0x19, 0x1e, 0x04, 0x8b, 0x62,
  0x80, 0x14, 0x45, 0x02, 0x42, 0x92, 0x0b, 0x42, 0xa4, 0x10, 0x32, 0x14,
  0x38, 0x08, 0x18, 0x4b, 0x0a, 0x32, 0x52, 0x88, 0x48, 0x90, 0x14, 0x20,
  0x43, 0x46, 0x88, 0xa5, 0x00, 0x19, 0x32, 0x42, 0xe4, 0x48, 0x0e, 0x90,
  0x91, 0x22, 0xc4, 0x50, 0x41, 0x51, 0x81, 0x8c, 0xe1, 0x83, 0xe5, 0x8a,
  0x04, 0x29, 0x46, 0x06, 0x51, 0x18, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00,
  0x1b, 0x8c, 0xe0, 0xff, 0xff, 0xff, 0xff, 0x07, 0x40, 0x02, 0xa8, 0x0d,
  0x84, 0xf0, 0xff, 0xff, 0xff, 0xff, 0x03, 0x20, 0x6d, 0x30, 0x86, 0xff,
  0xff, 0xff, 0xff, 0x1f, 0x00, 0x09, 0xa8, 0x00, 0x49, 0x18, 0x00, 0x00,
  0x03, 0x00, 0x00, 0x00, 0x13, 0x82, 0x60, 0x42, 0x20, 0x4c, 0x08, 0x06,
  0x00, 0x00, 0x00, 0x00, 0x89, 0x20, 0x00, 0x00, 0x44, 0x00, 0x00, 0x00,
  0x32, 0x22, 0x48, 0x09, 0x20, 0x64, 0x85, 0x04, 0x93, 0x22, 0xa4, 0x84,
  0x04, 0x93, 0x22, 0xe3, 0x84, 0xa1, 0x90, 0x14, 0x12, 0x4c, 0x8a, 0x8c,
  0x0b, 0x84, 0xa4, 0x4c, 0x10, 0x70, 0x23, 0x00, 0x25, 0x00, 0x14, 0x66,
  0x00, 0xe6, 0x08, 0xc0, 0x60, 0x8e, 0x00, 0x29, 0xc6, 0x20, 0x84, 0x14,
  0x42, 0xa6, 0x18, 0x80, 0x10, 0x52, 0x06, 0xa1, 0x32, 0x0c, 0x32, 0x48,
  0xdd, 0x34, 0x5c, 0xfe, 0x84, 0x3d, 0x84, 0xe4, 0xaf, 0x84, 0xb4, 0x12,
  0x93, 0x5f, 0xdc, 0x36, 0x2a, 0xc6, 0x18, 0x83, 0x50, 0xb9, 0x67, 0xb8,
  0xfc, 0x09, 0x7b, 0x08, 0xc9, 0x0f, 0x81, 0x66, 0x58, 0x08, 0x14, 0xb4,
  0xc2, 0x30, 0xe2, 0xdc, 0x18, 0x63, 0x10, 0x42, 0x06, 0xbd, 0x39, 0x82,
  0xa0, 0x18, 0x8e, 0x14, 0x42, 0x22, 0xc9, 0x81, 0x80, 0x61, 0x04, 0x62,
  0x98, 0xa9, 0x0d, 0xc6, 0x81, 0x1d, 0xc2, 0x61, 0x1e, 0xe6, 0xc1, 0x0d,
  0x68, 0xa1, 0x1c, 0xf0, 0x81, 0x1e, 0xea, 0x41, 0x1e, 0xca, 0x41, 0x0e,
  0x48, 0x81, 0x0f, 0xec, 0xa1, 0x1c, 0xc6, 0x81, 0x1e, 0xde, 0x41, 0x1e,
  0xf8, 0xc0, 0x1c, 0xd8, 0xe1, 0x1d, 0xc2, 0x81, 0x1e, 0xd8, 0x00, 0x0c,
  0xe8, 0xc0, 0x0f, 0xc0, 0xc0, 0x0f, 0xf4, 0x40, 0x0f, 0xda, 0x21, 0x1d,
  0xe0, 0x61, 0x1e, 0x7e, 0x81, 0x1e, 0xf2, 0x01, 0x1e, 0xca, 0x01, 0x05,
  0x64, 0x26, 0x31, 0x18, 0x07, 0x76, 0x08, 0x87, 0x79, 0x98, 0x07, 0x37,
  0xa0, 0x85, 0x72, 0xc0, 0x07, 0x7a, 0xa8, 0x07, 0x79, 0x28, 0x07, 0x39,
  0x20, 0x05, 0x3e, 0xb0, 0x87, 0x72, 0x18, 0x07, 0x7a, 0x78, 0x07, 0x79,
  0xe0, 0x03, 0x73, 0x60, 0x87, 0x77, 0x08, 0x07, 0x7a, 0x60, 0x03, 0x30,
  0xa0, 0x03, 0x3f, 0x00, 0x03, 0x3f, 0x40, 0x42, 0xb5, 0x74, 0x6f, 0x92,
  0xa6, 0x88, 0x12, 0x26, 0x9f, 0x05, 0x98, 0x67, 0x21, 0x22, 0x76, 0x02,
  0x26, 0x02, 0x05, 0x84, 0x72, 0x22, 0x10, 0x00, 0x13, 0x14, 0x72, 0xc0,
  0x87, 0x74, 0x60, 0x87, 0x36, 0x68, 0x87, 0x79, 0x68, 0x03, 0x72, 0xc0,
  0x87, 0x0d, 0xaf, 0x50, 0x0e, 0x6d, 0xd0, 0x0e, 0x7a, 0x50, 0x0e, 0x6d,
  0x00, 0x0f, 0x7a, 0x30, 0x07, 0x72, 0xa0, 0x07, 0x73, 0x20, 0x07, 0x6d,
  0x90, 0x0e, 0x71, 0xa0, 0x07, 0x73, 0x20, 0x07, 0x6d, 0x90, 0x0e, 0x78,
  0xa0, 0x07, 0x73, 0x20, 0x07, 0x6d, 0x90, 0x0e, 0x71, 0x60, 0x07, 0x7a,
  0x30, 0x07, 0x72, 0xd0, 0x06, 0xe9, 0x30, 0x07, 0x72, 0xa0, 0x07, 0x73,
  0x20, 0x07, 0x6d, 0x90, 0x0e, 0x76, 0x40, 0x07, 0x7a, 0x60, 0x07, 0x74,
  0xd0, 0x06, 0xe6, 0x10, 0x07, 0x76, 0xa0, 0x07, 0x73, 0x20, 0x07, 0x6d,
  0x60, 0x0e, 0x73, 0x20, 0x07, 0x7a, 0x30, 0x07, 0x72, 0xd0, 0x06, 0xe6,
  0x60, 0x07, 0x74, 0xa0, 0x07, 0x76, 0x40, 0x07, 0x6d, 0xe0, 0x0e, 0x78,
  0xa0, 0x07, 0x71, 0x60, 0x07, 0x7a, 0x30, 0x07, 0x72, 0xa0, 0x07, 0x76,
  0x40, 0x07, 0x43, 0x9e, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x86, 0x3c, 0x06, 0x10, 0x00, 0x01, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x0c, 0x79, 0x10, 0x20, 0x00, 0x04, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0xf2, 0x28, 0x40, 0x00, 0x04, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0xe4, 0x79, 0x80, 0x00, 0x18,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0xc8, 0x23, 0x01, 0x01,
  0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x16, 0x08, 0x00,
  0x0f, 0x00, 0x00, 0x00, 0x32, 0x1e, 0x98, 0x14, 0x19, 0x11, 0x4c, 0x90,
  0x8c, 0x09, 0x26, 0x47, 0xc6, 0x04, 0x43, 0x22, 0x25, 0x30, 0x02, 0x50,
  0x0c, 0x45, 0x50, 0x12, 0x65, 0x50, 0x1e, 0x05, 0x51, 0x2e, 0x85, 0x40,
  0xa5, 0x24, 0x46, 0x00, 0x8a, 0xa0, 0x10, 0x0a, 0x84, 0xf0, 0x0c, 0x00,
  0xe9, 0x19, 0x00, 0xda, 0x63, 0x21, 0x06, 0x11, 0x08, 0x04, 0x82, 0x20,
  0x00, 0x00, 0x00, 0x00, 0x79, 0x18, 0x00, 0x00, 0x78, 0x00, 0x00, 0x00,
  0x1a, 0x03, 0x4c, 0x90, 0x46, 0x02, 0x13, 0xc4, 0x8f, 0x0c, 0x6f, 0x0c,
  0x05, 0x4e, 0x2e, 0xcd, 0x2e, 0x8c, 0xae, 0x2c, 0x05, 0x24, 0xc6, 0x05,
  0xc7, 0x05, 0xc6, 0x25, 0x06, 0x04, 0x25, 0x6c, 0x6c, 0xc6, 0x26, 0xec,
  0x26, 0xe7, 0x26, 0x65, 0x43, 0x10, 0x4c, 0x10, 0x08, 0x63, 0x82, 0x40,
  0x1c, 0x1b, 0x84, 0x81, 0xd8, 0x20, 0x10, 0x04, 0x05, 0xb8, 0xb9, 0x09,
  0x02, 0x81, 0x6c, 0x18, 0x0e, 0x84, 0x98, 0x20, 0x60, 0x15, 0x07, 0xba,
  0x32, 0xbc, 0x09, 0x02, 0x91, 0x4c, 0x10, 0x08, 0x65, 0x83, 0x40, 0x34,
  0x1b, 0x12, 0x42, 0x59, 0x18, 0x62, 0x60, 0x08, 0x67, 0x43, 0xf0, 0x4c,
  0x10, 0x34, 0x8b, 0xc4, 0x5c, 0x58, 0x1b, 0xdc, 0x06, 0x84, 0x88, 0x24,
  0x86, 0x18, 0x08, 0x60, 0x43, 0x30, 0x6d, 0x20, 0x20, 0x00, 0xa0, 0x26,
  0x08, 0x02, 0xb0, 0x01, 0xd8, 0x30, 0x10, 0xd7, 0xb5, 0x21, 0xc0, 0x36,
  0x0c, 0x83, 0x95, 0x4d, 0x10, 0xb6, 0x6b, 0x43, 0xb0, 0x91, 0x68, 0x0b,
  0x4b, 0x73, 0x23, 0x42, 0x55, 0x84, 0x35, 0xf4, 0xf4, 0x24, 0x45, 0x34,
  0x41, 0x28, 0xa0, 0x09, 0x42, 0x11, 0x6d, 0x08, 0x88, 0x09, 0x42, 0x21,
  0x4d, 0x10, 0x8a, 0x69, 0x82, 0x40, 0x2c, 0x13, 0x04, 0x82, 0xd9, 0x20,
  0x90, 0x41, 0x19, 0x6c, 0x58, 0x08, 0xef, 0x03, 0x83, 0x30, 0x10, 0x83,
  0x61, 0x0c, 0x08, 0x30, 0x30, 0x83, 0x0d, 0xc1, 0xb0, 0x41, 0x20, 0x03,
  0x32, 0xd8, 0xb0, 0x0c, 0xde, 0x07, 0x06, 0x68, 0x20, 0x06, 0x83, 0x18,
  0x0c, 0x60, 0x90, 0x06, 0x1b, 0x84, 0x33, 0x50, 0x03, 0x26, 0x53, 0x56,
  0x5f, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x13, 0x84, 0x82, 0xda, 0xb0,
  0x10, 0x6c, 0xf0, 0xb5, 0x41, 0x18, 0x80, 0xc1, 0x30, 0x06, 0x04, 0x18,
  0x98, 0xc1, 0x86, 0xc0, 0x0d, 0x36, 0x0c, 0x6b, 0xf0, 0x06, 0xc0, 0x86,
  0xc2, 0xea, 0xe0, 0xa0, 0x02, 0x68, 0x98, 0xb1, 0xbd, 0x85, 0xd1, 0xcd,
  0x4d, 0x10, 0x88, 0x86, 0x45, 0x9a, 0xdb, 0x1c, 0xdd, 0xdc, 0x04, 0x81,
  0x70, 0x68, 0xcc, 0xa5, 0x9d, 0x7d, 0xb1, 0x91, 0xd1, 0x98, 0x4b, 0x3b,
  0xfb, 0x9a, 0xa3, 0x9b, 0x20, 0x10, 0x0f, 0x11, 0xba, 0x32, 0xbc, 0x2f,
  0xb7, 0x37, 0xb9, 0xb6, 0x0d, 0x8a, 0x1c, 0xcc, 0x01, 0x1d, 0xd4, 0x81,
  0x1d, 0x20, 0x77, 0x80, 0x07, 0x79, 0x30, 0x54, 0x61, 0x63, 0xb3, 0x6b,
  0x73, 0x49, 0x23, 0x2b, 0x73, 0xa3, 0x9b, 0x12, 0x04, 0x55, 0xc8, 0xf0,
  0x5c, 0xec, 0xca, 0xe4, 0xe6, 0xd2, 0xde, 0xdc, 0xa6, 0x04, 0x44, 0x13,
  0x32, 0x3c, 0x17, 0xbb, 0x30, 0x36, 0xbb, 0x32, 0xb9, 0x29, 0x41, 0x51,
  0x87, 0x0c, 0xcf, 0x65, 0x0e, 0x2d, 0x8c, 0xac, 0x4c, 0xae, 0xe9, 0x8d,
  0xac, 0x8c, 0x6d, 0x4a, 0x80, 0x94, 0x21, 0xc3, 0x73, 0x91, 0x2b, 0x9b,
  0x7b, 0xab, 0x93, 0x1b, 0x2b, 0x9b, 0x9b, 0x12, 0x50, 0x95, 0xc8, 0xf0,
  0x5c, 0xe8, 0xf2, 0xe0, 0xca, 0x82, 0xdc, 0xdc, 0xde, 0xe8, 0xc2, 0xe8,
  0xd2, 0xde, 0xdc, 0xe6, 0xa6, 0x04, 0x59, 0x1d, 0x32, 0x3c, 0x17, 0xbb,
  0xb4, 0xb2, 0xbb, 0x24, 0xb2, 0x29, 0xba, 0x30, 0xba, 0xb2, 0x29, 0xc1,
  0x56, 0x87, 0x0c, 0xcf, 0xa5, 0xcc, 0x8d, 0x4e, 0x2e, 0x0f, 0xea, 0x2d,
  0xcd, 0x8d, 0x6e, 0x6e, 0x4a, 0x00, 0x07, 0x5d, 0xc8, 0xf0, 0x5c, 0xc6,
  0xde, 0xea, 0xdc, 0xe8, 0xca, 0xe4, 0xe6, 0xa6, 0x04, 0x79, 0x00, 0x00,
  0x79, 0x18, 0x00, 0x00, 0x4c, 0x00, 0x00, 0x00, 0x33, 0x08, 0x80, 0x1c,
  0xc4, 0xe1, 0x1c, 0x66, 0x14, 0x01, 0x3d, 0x88, 0x43, 0x38, 0x84, 0xc3,
  0x8c, 0x42, 0x80, 0x07, 0x79, 0x78, 0x07, 0x73, 0x98, 0x71, 0x0c, 0xe6,
  0x00, 0x0f, 0xed, 0x10, 0x0e, 0xf4, 0x80, 0x0e, 0x33, 0x0c, 0x42, 0x1e,
  0xc2, 0xc1, 0x1d, 0xce, 0xa1, 0x1c, 0x66, 0x30, 0x05, 0x3d, 0x88, 0x43,
  0x38, 0x84, 0x83, 0x1b, 0xcc, 0x03, 0x3d, 0xc8, 0x43, 0x3d, 0x8c, 0x03,
  0x3d, 0xcc, 0x78, 0x8c, 0x74, 0x70, 0x07, 0x7b, 0x08, 0x07, 0x79, 0x48,
  0x87, 0x70, 0x70, 0x07, 0x7a, 0x70, 0x03, 0x76, 0x78, 0x87, 0x70, 0x20,
  0x87, 0x19, 0xcc, 0x11, 0x0e, 0xec, 0x90, 0x0e, 0xe1, 0x30, 0x0f, 0x6e,
  0x30, 0x0f, 0xe3, 0xf0, 0x0e, 0xf0, 0x50, 0x0e, 0x33, 0x10, 0xc4, 0x1d,
  0xde, 0x21, 0x1c, 0xd8, 0x21, 0x1d, 0xc2, 0x61, 0x1e, 0x66, 0x30, 0x89,
  0x3b, 0xbc, 0x83, 0x3b, 0xd0, 0x43, 0x39, 0xb4, 0x03, 0x3c, 0xbc, 0x83,
  0x3c, 0x84, 0x03, 0x3b, 0xcc, 0xf0, 0x14, 0x76, 0x60, 0x07, 0x7b, 0x68,
  0x07, 0x37, 0x68, 0x87, 0x72, 0x68, 0x07, 0x37, 0x80, 0x87, 0x70, 0x90,
  0x87, 0x70, 0x60, 0x07, 0x76, 0x28, 0x07, 0x76, 0xf8, 0x05, 0x76, 0x78,
  0x87, 0x77, 0x80, 0x87, 0x5f, 0x08, 0x87, 0x71, 0x18, 0x87, 0x72, 0x98,
  0x87, 0x79, 0x98, 0x81, 0x2c, 0xee, 0xf0, 0x0e, 0xee, 0xe0, 0x0e, 0xf5,
  0xc0, 0x0e, 0xec, 0x30, 0x03, 0x62, 0xc8, 0xa1, 0x1c, 0xe4, 0xa1, 0x1c,
  0xcc, 0xa1, 0x1c, 0xe4, 0xa1, 0x1c, 0xdc, 0x61, 0x1c, 0xca, 0x21, 0x1c,
  0xc4, 0x81, 0x1d, 0xca, 0x61, 0x06, 0xd6, 0x90, 0x43, 0x39, 0xc8, 0x43,
  0x39, 0x98, 0x43, 0x39, 0xc8, 0x43, 0x39, 0xb8, 0xc3, 0x38, 0x94, 0x43,
  0x38, 0x88, 0x03, 0x3b, 0x94, 0xc3, 0x2f, 0xbc, 0x83, 0x3c, 0xfc, 0x82,
  0x3b, 0xd4, 0x03, 0x3b, 0xb0, 0xc3, 0x8c, 0xc8, 0x21, 0x07, 0x7c, 0x70,
  0x03, 0x72, 0x10, 0x87, 0x73, 0x70, 0x03, 0x7b, 0x08, 0x07, 0x79, 0x60,
  0x87, 0x70, 0xc8, 0x87, 0x77, 0xa8, 0x07, 0x7a, 0x98, 0x81, 0x3c, 0xe4,
  0x80, 0x0f, 0x6e, 0x40, 0x0f, 0xe5, 0xd0, 0x0e, 0xf0, 0x00, 0x00, 0x00,
  0x71, 0x20, 0x00, 0x00, 0x15, 0x00, 0x00, 0x00, 0x56, 0x20, 0x0d, 0x97,
  0xef, 0x3c, 0xbe, 0x10, 0x11, 0xc0, 0x44, 0x84, 0x40, 0x33, 0x2c, 0x84,
  0x05, 0x4c, 0xc3, 0xe5, 0x3b, 0x8f, 0xbf, 0x38, 0xc0, 0x20, 0x36, 0x0f,
  0x35, 0xf9, 0xc5, 0x6d, 0x1b, 0x01, 0x34, 0x5c, 0xbe, 0xf3, 0xf8, 0x12,
  0xc0, 0x3c, 0x0b, 0xe1, 0x17, 0xb7, 0x6d, 0x02, 0xd5, 0x70, 0xf9, 0xce,
  0xe3, 0x4b, 0x93, 0x13, 0x11, 0x28, 0x35, 0x3d, 0xd4, 0xe4, 0x17, 0xb7,
  0x6d, 0x03, 0xcf, 0x70, 0xf9, 0xce, 0xe3, 0x53, 0x0d, 0x10, 0x61, 0x7e,
  0x71, 0xdb, 0x06, 0x40, 0x30, 0x00, 0xd2, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x48, 0x41, 0x53, 0x48, 0x14, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x25, 0xe6, 0x79, 0x2d, 0x0e, 0x6b, 0x12, 0x67, 0xa9, 0xac, 0x59, 0xb4,
  0x1e, 0x07, 0xeb, 0x3d, 0x44, 0x58, 0x49, 0x4c, 0x14, 0x07, 0x00, 0x00,
  0x60, 0x00, 0x00, 0x00, 0xc5, 0x01, 0x00, 0x00, 0x44, 0x58, 0x49, 0x4c,
  0x00, 0x01, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0xfc, 0x06, 0x00, 0x00,
  0x42, 0x43, 0xc0, 0xde, 0x21, 0x0c, 0x00, 0x00, 0xbc, 0x01, 0x00, 0x00,
  0x0b, 0x82, 0x20, 0x00, 0x02, 0x00, 0x00, 0x00, 0x13, 0x00, 0x00, 0x00,
  0x07, 0x81, 0x23, 0x91, 0x41, 0xc8, 0x04, 0x49, 0x06, 0x10, 0x32, 0x39,
  0x92, 0x01, 0x84, 0x0c, 0x25, 0x05, 0x08, 0x19, 0x1e, 0x04, 0x8b, 0x62,
  0x80, 0x14, 0x45, 0x02, 0x42, 0x92, 0x0b, 0x42, 0xa4, 0x10, 0x32, 0x14,
  0x38, 0x08, 0x18, 0x4b, 0x0a, 0x32, 0x52, 0x88, 0x48, 0x90, 0x14, 0x20,
  0x43, 0x46, 0x88, 0xa5, 0x00, 0x19, 0x32, 0x42, 0xe4, 0x48, 0x0e, 0x90,
  0x91, 0x22, 0xc4, 0x50, 0x41, 0x51, 0x81, 0x8c, 0xe1, 0x83, 0xe5, 0x8a,
  0x04, 0x29, 0x46, 0x06, 0x51, 0x18, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00,
  0x1b, 0x8c, 0xe0, 0xff, 0xff, 0xff, 0xff, 0x07, 0x40, 0x02, 0xa8, 0x0d,
  0x84, 0xf0, 0xff, 0xff, 0xff, 0xff, 0x03, 0x20, 0x6d, 0x30, 0x86, 0xff,
  0xff, 0xff, 0xff, 0x1f, 0x00, 0x09, 0xa8, 0x00, 0x49, 0x18, 0x00, 0x00,
  0x03, 0x00, 0x00, 0x00, 0x13, 0x82, 0x60, 0x42, 0x20, 0x4c, 0x08, 0x06,
  0x00, 0x00, 0x00, 0x00, 0x89, 0x20, 0x00, 0x00, 0x44, 0x00, 0x00, 0x00,
  0x32, 0x22, 0x48, 0x09, 0x20, 0x64, 0x85, 0x04, 0x93, 0x22, 0xa4, 0x84,
  0x04, 0x93, 0x22, 0xe3, 0x84, 0xa1, 0x90, 0x14, 0x12, 0x4c, 0x8a, 0x8c,
  0x0b, 0x84, 0xa4, 0x4c, 0x10, 0x70, 0x23, 0x00, 0x25, 0x00, 0x14, 0x66,
  0x00, 0xe6, 0x08, 0xc0, 0x60, 0x8e, 0x00, 0x29, 0xc6, 0x20, 0x84, 0x14,
  0x42, 0xa6, 0x18, 0x80, 0x10, 0x52, 0x06, 0xa1, 0x32, 0x0c, 0x32, 0x48,
  0xdd, 0x34, 0x5c, 0xfe, 0x84, 0x3d, 0x84, 0xe4, 0xaf, 0x84, 0xb4, 0x12,
  0x93, 0x5f, 0xdc, 0x36, 0x2a, 0xc6, 0x18, 0x83, 0x50, 0xb9, 0x67, 0xb8,
  0xfc, 0x09, 0x7b, 0x08, 0xc9, 0x0f, 0x81, 0x66, 0x58, 0x08, 0x14, 0xb4,
  0xc2, 0x30, 0xe2, 0xdc, 0x18, 0x63, 0x10, 0x42, 0x06, 0xbd, 0x39, 0x82,
  0xa0, 0x18, 0x8e, 0x14, 0x42, 0x22, 0xc9, 0x81, 0x80, 0x61, 0x04, 0x62,
  0x98, 0xa9, 0x0d, 0xc6, 0x81, 0x1d, 0xc2, 0x61, 0x1e, 0xe6, 0xc1, 0x0d,
  0x68, 0xa1, 0x1c, 0xf0, 0x81, 0x1e, 0xea, 0x41, 0x1e, 0xca, 0x41, 0x0e,
  0x48, 0x81, 0x0f, 0xec, 0xa1, 0x1c, 0xc6, 0x81, 0x1e, 0xde, 0x41, 0x1e,
  0xf8, 0xc0, 0x1c, 0xd8, 0xe1, 0x1d, 0xc2, 0x81, 0x1e, 0xd8, 0x00, 0x0c,
  0xe8, 0xc0, 0x0f, 0xc0, 0xc0, 0x0f, 0xf4, 0x40, 0x0f, 0xda, 0x21, 0x1d,
  0xe0, 0x61, 0x1e, 0x7e, 0x81, 0x1e, 0xf2, 0x01, 0x1e, 0xca, 0x01, 0x05,
  0x64, 0x26, 0x31, 0x18, 0x07, 0x76, 0x08, 0x87, 0x79, 0x98, 0x07, 0x37,
  0xa0, 0x85, 0x72, 0xc0, 0x07, 0x7a, 0xa8, 0x07, 0x79, 0x28, 0x07, 0x39,
  0x20, 0x05, 0x3e, 0xb0, 0x87, 0x72, 0x18, 0x07, 0x7a, 0x78, 0x07, 0x79,
  0xe0, 0x03, 0x73, 0x60, 0x87, 0x77, 0x08, 0x07, 0x7a, 0x60, 0x03, 0x30,
  0xa0, 0x03, 0x3f, 0x00, 0x03, 0x3f, 0x40, 0x42, 0xb5, 0x74, 0x6f, 0x92,
  0xa6, 0x88, 0x12, 0x26, 0x9f, 0x05, 0x98, 0x67, 0x21, 0x22, 0x76, 0x02,
  0x26, 0x02, 0x05, 0x84, 0x72, 0x22, 0x10, 0x00, 0x13, 0x14, 0x72, 0xc0,
  0x87, 0x74, 0x60, 0x87, 0x36, 0x68, 0x87, 0x79, 0x68, 0x03, 0x72, 0xc0,
  0x87, 0x0d, 0xaf, 0x50, 0x0e, 0x6d, 0xd0, 0x0e, 0x7a, 0x50, 0x0e, 0x6d,
  0x00, 0x0f, 0x7a, 0x30, 0x07, 0x72, 0xa0, 0x07, 0x73, 0x20, 0x07, 0x6d,
  0x90, 0x0e, 0x71, 0xa0, 0x07, 0x73, 0x20, 0x07, 0x6d, 0x90, 0x0e, 0x78,
  0xa0, 0x07, 0x73, 0x20, 0x07, 0x6d, 0x90, 0x0e, 0x71, 0x60, 0x07, 0x7a,
  0x30, 0x07, 0x72, 0xd0, 0x06, 0xe9, 0x30, 0x07, 0x72, 0xa0, 0x07, 0x73,
  0x20, 0x07, 0x6d, 0x90, 0x0e, 0x76, 0x40, 0x07, 0x7a, 0x60, 0x07, 0x74,
  0xd0, 0x06, 0xe6, 0x10, 0x07, 0x76, 0xa0, 0x07, 0x73, 0x20, 0x07, 0x6d,
  0x60, 0x0e, 0x73, 0x20, 0x07, 0x7a, 0x30, 0x07, 0x72, 0xd0, 0x06, 0xe6,
  0x60, 0x07, 0x74, 0xa0, 0x07, 0x76, 0x40, 0x07, 0x6d, 0xe0, 0x0e, 0x78,
  0xa0, 0x07, 0x71, 0x60, 0x07, 0x7a, 0x30, 0x07, 0x72, 0xa0, 0x07, 0x76,
  0x40, 0x07, 0x43, 0x9e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x86, 0x3c, 0x06, 0x10, 0x00, 0x01, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x0c, 0x79, 0x10, 0x20, 0x00, 0x04, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0xf2, 0x28, 0x40, 0x00, 0x04, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0xe4, 0x79, 0x80, 0x00, 0x18,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0xc8, 0x23, 0x01, 0x01,
  0x30, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x16, 0x08, 0x00,
  0x0e, 0x00, 0x00, 0x00, 0x32, 0x1e, 0x98, 0x14, 0x19, 0x11, 0x4c, 0x90,
  0x8c, 0x09, 0x26, 0x47, 0xc6, 0x04, 0x43, 0x22, 0x25, 0x30, 0x02, 0x50,
  0x10, 0xc5, 0x50, 0x04, 0x25, 0x51, 0x06, 0xe5, 0x41, 0xa5, 0x24, 0x46,
  0x00, 0x8a, 0xa0, 0x10, 0x0a, 0x84, 0xf0, 0x0c, 0x00, 0xe9, 0x19, 0x00,
  0xda, 0x63, 0x21, 0x06, 0x11, 0x08, 0x04, 0x82, 0x20, 0x00, 0x00, 0x00,
  0x79, 0x18, 0x00, 0x00, 0x59, 0x00, 0x00, 0x00, 0x1a, 0x03, 0x4c, 0x90,
  0x46, 0x02, 0x13, 0xc4, 0x8f, 0x0c, 0x6f, 0x0c, 0x05, 0x4e, 0x2e, 0xcd,
  0x2e, 0x8c, 0xae, 0x2c, 0x05, 0x24, 0xc6, 0x05, 0xc7, 0x05, 0xc6, 0x25,
  0x06, 0x04, 0x25, 0x6c, 0x6c, 0xc6, 0x26, 0xec, 0x26, 0xe7, 0x26, 0x65,
  0x43, 0x10, 0x4c, 0x10, 0x08, 0x63, 0x82, 0x40, 0x1c, 0x1b, 0x84, 0x81,
  0x98, 0x20, 0x10, 0xc8, 0x06, 0x61, 0x30, 0x28, 0xc0, 0xcd, 0x4d, 0x10,
  0x88, 0x64, 0xc3, 0x80, 0x24, 0xc4, 0x04, 0x01, 0x9b, 0x08, 0x4c, 0x10,
  0x08, 0x65, 0x82, 0x40, 0x2c, 0x1b, 0x04, 0xc2, 0xd9, 0x90, 0x10, 0x0b,
  0xd3, 0x10, 0x43, 0x43, 0x3c, 0x1b, 0x02, 0x68, 0x82, 0xa0, 0x51, 0x1b,
  0x10, 0x42, 0x62, 0x1a, 0x62, 0x20, 0x80, 0x0d, 0xc1, 0xb4, 0x81, 0x88,
  0x00, 0x80, 0x9a, 0x20, 0x6c, 0xd5, 0x86, 0xc0, 0x9a, 0x20, 0x08, 0x00,
  0x89, 0xb6, 0xb0, 0x34, 0x37, 0x22, 0x54, 0x45, 0x58, 0x43, 0x4f, 0x4f,
  0x52, 0x44, 0x13, 0x84, 0xc2, 0x99, 0x20, 0x14, 0xcf, 0x86, 0x80, 0x98,
  0x20, 0x14, 0xd0, 0x04, 0xa1, 0x88, 0x26, 0x08, 0x04, 0x33, 0x41, 0x20,
  0x9a, 0x0d, 0x02, 0x18, 0x84, 0xc1, 0x86, 0x85, 0xd0, 0x36, 0xae, 0xf3,
  0x86, 0x8f, 0xe0, 0xc4, 0x60, 0x43, 0x30, 0x6c, 0x10, 0xc0, 0x00, 0x0c,
  0x36, 0x2c, 0x83, 0xb6, 0x71, 0x64, 0xe0, 0x0d, 0xde, 0xc0, 0x95, 0xc1,
  0x06, 0x61, 0x0c, 0xcc, 0x80, 0xc9, 0x94, 0xd5, 0x17, 0x55, 0x98, 0xdc,
  0x59, 0x19, 0xdd, 0x04, 0xa1, 0x90, 0x36, 0x2c, 0x04, 0x1a, 0x6c, 0x69,
  0xd0, 0x71, 0xc3, 0x47, 0x70, 0x62, 0xb0, 0x21, 0x50, 0x83, 0x0d, 0xc3,
  0x19, 0xac, 0x01, 0xb0, 0xa1, 0xc0, 0x32, 0x36, 0xa8, 0x80, 0x2a, 0x6c,
  0x6c, 0x76, 0x6d, 0x2e, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x53, 0x82, 0xa0,
  0x0a, 0x19, 0x9e, 0x8b, 0x5d, 0x99, 0xdc, 0x5c, 0xda, 0x9b, 0xdb, 0x94,
  0x80, 0x68, 0x42, 0x86, 0xe7, 0x62, 0x17, 0xc6, 0x66, 0x57, 0x26, 0x37,
  0x25, 0x30, 0xea, 0x90, 0xe1, 0xb9, 0xcc, 0xa1, 0x85, 0x91, 0x95, 0xc9,
  0x35, 0xbd, 0x91, 0x95, 0xb1, 0x4d, 0x09, 0x92, 0x32, 0x64, 0x78, 0x2e,
  0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x53, 0x02, 0xaa,
  0x0e, 0x19, 0x9e, 0x8b, 0x5d, 0x5a, 0xd9, 0x5d, 0x12, 0xd9, 0x14, 0x5d,
  0x18, 0x5d, 0xd9, 0x94, 0xc0, 0xaa, 0x43, 0x86, 0xe7, 0x52, 0xe6, 0x46,
  0x27, 0x97, 0x07, 0xf5, 0x96, 0xe6, 0x46, 0x37, 0x37, 0x25, 0x60, 0x03,
  0x00, 0x00, 0x00, 0x00, 0x79, 0x18, 0x00, 0x00, 0x4c, 0x00, 0x00, 0x00,
  0x33, 0x08, 0x80, 0x1c, 0xc4, 0xe1, 0x1c, 0x66, 0x14, 0x01, 0x3d, 0x88,
  0x43, 0x38, 0x84, 0xc3, 0x8c, 0x42, 0x80, 0x07, 0x79, 0x78, 0x07, 0x73,
  0x98, 0x71, 0x0c, 0xe6, 0x00, 0x0f, 0xed, 0x10, 0x0e, 0xf4, 0x80, 0x0e,
  0x33, 0x0c, 0x42, 0x1e, 0xc2, 0xc1, 0x1d, 0xce, 0xa1, 0x1c, 0x66, 0x30,
  0x05, 0x3d, 0x88, 0x43, 0x38, 0x84, 0x83, 0x1b, 0xcc, 0x03, 0x3d, 0xc8,
  0x43, 0x3d, 0x8c, 0x03, 0x3d, 0xcc, 0x78, 0x8c, 0x74, 0x70, 0x07, 0x7b,
  0x08, 0x07, 0x79, 0x48, 0x87, 0x70, 0x70, 0x07, 0x7a, 0x70, 0x03, 0x76,
  0x78, 0x87, 0x70, 0x20, 0x87, 0x19, 0xcc, 0x11, 0x0e, 0xec, 0x90, 0x0e,
  0xe1, 0x30, 0x0f, 0x6e, 0x30, 0x0f, 0xe3, 0xf0, 0x0e, 0xf0, 0x50, 0x0e,
  0x33, 0x10, 0xc4, 0x1d, 0xde, 0x21, 0x1c, 0xd8, 0x21, 0x1d, 0xc2, 0x61,
  0x1e, 0x66, 0x30, 0x89, 0x3b, 0xbc, 0x83, 0x3b, 0xd0, 0x43, 0x39, 0xb4,
  0x03, 0x3c, 0xbc, 0x83, 0x3c, 0x84, 0x03, 0x3b, 0xcc, 0xf0, 0x14, 0x76,
  0x60, 0x07, 0x7b, 0x68, 0x07, 0x37, 0x68, 0x87, 0x72, 0x68, 0x07, 0x37,
  0x80, 0x87, 0x70, 0x90, 0x87, 0x70, 0x60, 0x07, 0x76, 0x28, 0x07, 0x76,
  0xf8, 0x05, 0x76, 0x78, 0x87, 0x77, 0x80, 0x87, 0x5f, 0x08, 0x87, 0x71,
  0x18, 0x87, 0x72, 0x98, 0x87, 0x79, 0x98, 0x81, 0x2c, 0xee, 0xf0, 0x0e,
  0xee, 0xe0, 0x0e, 0xf5, 0xc0, 0x0e, 0xec, 0x30, 0x03, 0x62, 0xc8, 0xa1,
  0x1c, 0xe4, 0xa1, 0x1c, 0xcc, 0xa1, 0x1c, 0xe4, 0xa1, 0x1c, 0xdc, 0x61,
  0x1c, 0xca, 0x21, 0x1c, 0xc4, 0x81, 0x1d, 0xca, 0x61, 0x06, 0xd6, 0x90,
  0x43, 0x39, 0xc8, 0x43, 0x39, 0x98, 0x43, 0x39, 0xc8, 0x43, 0x39, 0xb8,
  0xc3, 0x38, 0x94, 0x43, 0x38, 0x88, 0x03, 0x3b, 0x94, 0xc3, 0x2f, 0xbc,
  0x83, 0x3c, 0xfc, 0x82, 0x3b, 0xd4, 0x03, 0x3b, 0xb0, 0xc3, 0x8c, 0xc8,
  0x21, 0x07, 0x7c, 0x70, 0x03, 0x72, 0x10, 0x87, 0x73, 0x70, 0x03, 0x7b,
  0x08, 0x07, 0x79, 0x60, 0x87, 0x70, 0xc8, 0x87, 0x77, 0xa8, 0x07, 0x7a,
  0x98, 0x81, 0x3c, 0xe4, 0x80, 0x0f, 0x6e, 0x40, 0x0f, 0xe5, 0xd0, 0x0e,
  0xf0, 0x00, 0x00, 0x00, 0x71, 0x20, 0x00, 0x00, 0x15, 0x00, 0x00, 0x00,
  0x56, 0x20, 0x0d, 0x97, 0xef, 0x3c, 0xbe, 0x10, 0x11, 0xc0, 0x44, 0x84,
  0x40, 0x33, 0x2c, 0x84, 0x05, 0x4c, 0xc3, 0xe5, 0x3b, 0x8f, 0xbf, 0x38,
  0xc0, 0x20, 0x36, 0x0f, 0x35, 0xf9, 0xc5, 0x6d, 0x1b, 0x01, 0x34, 0x5c,
  0xbe, 0xf3, 0xf8, 0x12, 0xc0, 0x3c, 0x0b, 0xe1, 0x17, 0xb7, 0x6d, 0x02,
  0xd5, 0x70, 0xf9, 0xce, 0xe3, 0x4b, 0x93, 0x13, 0x11, 0x28, 0x35, 0x3d,
  0xd4, 0xe4, 0x17, 0xb7, 0x6d, 0x03, 0xcf, 0x70, 0xf9, 0xce, 0xe3, 0x53,
  0x0d, 0x10, 0x61, 0x7e, 0x71, 0xdb, 0x06, 0x40, 0x30, 0x00, 0xd2, 0x00,
  0x61, 0x20, 0x00, 0x00, 0x48, 0x00, 0x00, 0x00, 0x13, 0x04, 0x41, 0x2c,
  0x10, 0x00, 0x00, 0x00, 0x0d, 0x00, 0x00, 0x00, 0x14, 0x47, 0x00, 0xa8,
  0x94, 0x40, 0x19, 0x10, 0x29, 0x87, 0xc2, 0x9b, 0x01, 0x28, 0x84, 0x52,
  0x28, 0x39, 0x1a, 0x63, 0x04, 0x20, 0x08, 0x82, 0x24, 0x18, 0x8c, 0x11,
  0x80, 0x20, 0x08, 0x82, 0x60, 0x30, 0x46, 0x00, 0x82, 0x20, 0x08, 0x83,
  0xc1, 0x18, 0x01, 0x08, 0x82, 0xa0, 0xfb, 0x0b, 0x33, 0x00, 0x00, 0x00,
  0x23, 0x06, 0x09, 0x00, 0x82, 0x60, 0x20, 0x7d, 0x46, 0xd5, 0x75, 0xce,
  0x88, 0x41, 0x02, 0x80, 0x20, 0x18, 0x48, 0x60, 0x70, 0x34, 0x9e, 0xf7,
  0x8c, 0x18, 0x24, 0x00, 0x08, 0x82, 0x81, 0x51, 0x06, 0x0a, 0x18, 0x7c,
  0xd7, 0x32, 0x62, 0x90, 0x00, 0x20, 0x08, 0x06, 0x86, 0x19, 0x2c, 0x61,
  0x00, 0x06, 0x10, 0x33, 0x62, 0x90, 0x00, 0x20, 0x08, 0x06, 0xc6, 0x19,
  0x30, 0x61, 0x10, 0x06, 0x59, 0x33, 0x62, 0x90, 0x00, 0x20, 0x08, 0x06,
  0x06, 0x1a, 0x34, 0x62, 0x20, 0x06, 0x92, 0x33, 0x62, 0x90, 0x00, 0x20,
  0x08, 0x06, 0x46, 0x1a, 0x38, 0x63, 0x30, 0x06, 0xda, 0x33, 0x62, 0x90,
  0x00, 0x20, 0x08, 0x06, 0x86, 0x1a, 0x3c, 0x64, 0x40, 0x06, 0x13, 0x34,
  0x62, 0xf0, 0x00, 0x20, 0x08, 0x06, 0x0f, 0x1a, 0x48, 0xc8, 0x61, 0x14,
  0x49, 0x52, 0x06, 0x65, 0x10, 0x25, 0xa3, 0x09, 0xc1, 0x60, 0x01, 0x03,
  0x1f, 0x0b, 0x1c, 0xf9, 0x8c, 0x18, 0x18, 0x00, 0x08, 0x82, 0x81, 0xd2,
  0x06, 0x57, 0x60, 0x41, 0x24, 0x1f, 0x9b, 0x82, 0xf8, 0xd8, 0x30, 0xc8,
  0xc7, 0x82, 0x44, 0x3e, 0x16, 0x0c, 0xf2, 0x19, 0x31, 0x48, 0x00, 0x10,
  0x04, 0x03, 0x84, 0x0e, 0xb2, 0x37, 0x78, 0x83, 0x33, 0x70, 0x46, 0x0c,
  0x12, 0x00, 0x04, 0xc1, 0x00, 0xa1, 0x83, 0xec, 0x0d, 0xde, 0xe0, 0x6b,
  0x46, 0x0c, 0x12, 0x00, 0x04, 0xc1, 0x00, 0xa1, 0x83, 0xec, 0x0d, 0xde,
  0xc0, 0x0c, 0x98, 0x11, 0x83, 0x04, 0x00, 0x41, 0x30, 0x40, 0xe8, 0x20,
  0x7b, 0x83, 0x37, 0xf0, 0x02, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
};
static const unsigned int shader_sdf_frag_dxil_len = 3972;
