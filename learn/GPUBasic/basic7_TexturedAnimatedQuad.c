#define SDL_MAIN_USE_CALLBACKS 1
#include <SDL3/SDL.h>
#include <SDL3/SDL_main.h>
#include <math.h>

// Matrix Math Structures and Functions
typedef struct Matrix4x4
{
    float m11, m12, m13, m14;
    float m21, m22, m23, m24;
    float m31, m32, m33, m34;
    float m41, m42, m43, m44;
} Matrix4x4;

typedef struct
{
    float x, y, z;
    float u, v;
} PositionTextureVertex;

typedef struct FragMultiplyUniform
{
    float r, g, b, a;
} FragMultiplyUniform;

typedef struct Context
{
    SDL_Window *Window;
    SDL_GPUDevice *Device;
    float last_tick;
    float current_tick;
    float delta_time;
    
    bool app_in_background;
    
    // Animation time
    float t;
    
    // Rendering state
    SDL_GPUGraphicsPipeline *Pipeline;
    SDL_GPUBuffer *VertexBuffer;
    SDL_GPUBuffer *IndexBuffer;
    SDL_GPUTexture *Texture;
    SDL_GPUSampler *Sampler;
} Context;

// Matrix Math Functions
Matrix4x4 Matrix4x4_Multiply(Matrix4x4 matrix1, Matrix4x4 matrix2)
{
    Matrix4x4 result;
    result.m11 = matrix1.m11 * matrix2.m11 + matrix1.m12 * matrix2.m21 + matrix1.m13 * matrix2.m31 + matrix1.m14 * matrix2.m41;
    result.m12 = matrix1.m11 * matrix2.m12 + matrix1.m12 * matrix2.m22 + matrix1.m13 * matrix2.m32 + matrix1.m14 * matrix2.m42;
    result.m13 = matrix1.m11 * matrix2.m13 + matrix1.m12 * matrix2.m23 + matrix1.m13 * matrix2.m33 + matrix1.m14 * matrix2.m43;
    result.m14 = matrix1.m11 * matrix2.m14 + matrix1.m12 * matrix2.m24 + matrix1.m13 * matrix2.m34 + matrix1.m14 * matrix2.m44;
    
    result.m21 = matrix1.m21 * matrix2.m11 + matrix1.m22 * matrix2.m21 + matrix1.m23 * matrix2.m31 + matrix1.m24 * matrix2.m41;
    result.m22 = matrix1.m21 * matrix2.m12 + matrix1.m22 * matrix2.m22 + matrix1.m23 * matrix2.m32 + matrix1.m24 * matrix2.m42;
    result.m23 = matrix1.m21 * matrix2.m13 + matrix1.m22 * matrix2.m23 + matrix1.m23 * matrix2.m33 + matrix1.m24 * matrix2.m43;
    result.m24 = matrix1.m21 * matrix2.m14 + matrix1.m22 * matrix2.m24 + matrix1.m23 * matrix2.m34 + matrix1.m24 * matrix2.m44;
    
    result.m31 = matrix1.m31 * matrix2.m11 + matrix1.m32 * matrix2.m21 + matrix1.m33 * matrix2.m31 + matrix1.m34 * matrix2.m41;
    result.m32 = matrix1.m31 * matrix2.m12 + matrix1.m32 * matrix2.m22 + matrix1.m33 * matrix2.m32 + matrix1.m34 * matrix2.m42;
    result.m33 = matrix1.m31 * matrix2.m13 + matrix1.m32 * matrix2.m23 + matrix1.m33 * matrix2.m33 + matrix1.m34 * matrix2.m43;
    result.m34 = matrix1.m31 * matrix2.m14 + matrix1.m32 * matrix2.m24 + matrix1.m33 * matrix2.m34 + matrix1.m34 * matrix2.m44;
    
    result.m41 = matrix1.m41 * matrix2.m11 + matrix1.m42 * matrix2.m21 + matrix1.m43 * matrix2.m31 + matrix1.m44 * matrix2.m41;
    result.m42 = matrix1.m41 * matrix2.m12 + matrix1.m42 * matrix2.m22 + matrix1.m43 * matrix2.m32 + matrix1.m44 * matrix2.m42;
    result.m43 = matrix1.m41 * matrix2.m13 + matrix1.m42 * matrix2.m23 + matrix1.m43 * matrix2.m33 + matrix1.m44 * matrix2.m43;
    result.m44 = matrix1.m41 * matrix2.m14 + matrix1.m42 * matrix2.m24 + matrix1.m43 * matrix2.m34 + matrix1.m44 * matrix2.m44;
    
    return result;
}

Matrix4x4 Matrix4x4_CreateRotationZ(float radians)
{
    float cos_r = SDL_cosf(radians);
    float sin_r = SDL_sinf(radians);
    
    Matrix4x4 result = {
        cos_r, sin_r, 0, 0,
        -sin_r, cos_r, 0, 0,
        0, 0, 1, 0,
        0, 0, 0, 1
    };
    return result;
}

Matrix4x4 Matrix4x4_CreateTranslation(float x, float y, float z)
{
    Matrix4x4 result = {
        1, 0, 0, 0,
        0, 1, 0, 0,
        0, 0, 1, 0,
        x, y, z, 1
    };
    return result;
}

// Image loading function
SDL_Surface *LoadImage(const char *imageFilename, int desiredChannels)
{
    char fullPath[256];
    SDL_Surface *result;
    SDL_PixelFormat format;

    SDL_snprintf(fullPath, sizeof(fullPath), "assets/images/%s", imageFilename);

    result = SDL_LoadBMP(fullPath);
    if (result == NULL)
    {
        SDL_Log("Failed to load BMP: %s", SDL_GetError());
        return NULL;
    }

    if (desiredChannels == 4)
    {
        format = SDL_PIXELFORMAT_ABGR8888;
    }
    else
    {
        SDL_assert(!"Unexpected desiredChannels");
        SDL_DestroySurface(result);
        return NULL;
    }
    if (result->format != format)
    {
        SDL_Surface *next = SDL_ConvertSurface(result, format);
        SDL_DestroySurface(result);
        result = next;
    }

    return result;
}

// Shader loading function
SDL_GPUShader *LoadShader(SDL_GPUDevice *device, const char *filename, SDL_GPUShaderStage stage, Uint32 samplerCount, Uint32 uniformBufferCount)
{
    char fullPath[256];
    SDL_snprintf(fullPath, sizeof(fullPath), "assets/shaders/%s", filename);

    size_t codeSize;
    void *code = SDL_LoadFile(fullPath, &codeSize);
    if (code == NULL)
    {
        SDL_Log("Failed to load shader from disk! %s", fullPath);
        return NULL;
    }

    SDL_GPUShaderCreateInfo shaderInfo = {
        .code = code,
        .code_size = codeSize,
        .entrypoint = "main0", // MSL compiled shaders use main0
        .format = SDL_GPU_SHADERFORMAT_MSL,
        .stage = stage,
        .num_samplers = samplerCount,
        .num_uniform_buffers = uniformBufferCount,
        .num_storage_buffers = 0,
        .num_storage_textures = 0
    };

    SDL_GPUShader *shader = SDL_CreateGPUShader(device, &shaderInfo);
    if (shader == NULL)
    {
        SDL_Log("Failed to create shader! Error: %s", SDL_GetError());
        SDL_free(code);
        return NULL;
    }

    SDL_free(code);
    return shader;
}

int CreatePipeline(Context *context)
{
    // Create the shaders
    SDL_GPUShader *vertexShader = LoadShader(context->Device, "TexturedQuadWithMatrix.vert.msl", SDL_GPU_SHADERSTAGE_VERTEX, 0, 1);
    if (vertexShader == NULL)
    {
        SDL_Log("Failed to create vertex shader!");
        return -1;
    }

    SDL_GPUShader *fragmentShader = LoadShader(context->Device, "TexturedQuadWithMultiplyColor.frag.msl", SDL_GPU_SHADERSTAGE_FRAGMENT, 1, 1);
    if (fragmentShader == NULL)
    {
        SDL_Log("Failed to create fragment shader!");
        SDL_ReleaseGPUShader(context->Device, vertexShader);
        return -1;
    }

    // Create the pipeline
    SDL_GPUGraphicsPipelineCreateInfo pipelineCreateInfo = {
        .target_info = {
            .num_color_targets = 1,
            .color_target_descriptions = (SDL_GPUColorTargetDescription[]){{
                .format = SDL_GetGPUSwapchainTextureFormat(context->Device, context->Window),
                .blend_state = {
                    .enable_blend = true,
                    .alpha_blend_op = SDL_GPU_BLENDOP_ADD,
                    .color_blend_op = SDL_GPU_BLENDOP_ADD,
                    .src_color_blendfactor = SDL_GPU_BLENDFACTOR_SRC_ALPHA,
                    .src_alpha_blendfactor = SDL_GPU_BLENDFACTOR_SRC_ALPHA,
                    .dst_color_blendfactor = SDL_GPU_BLENDFACTOR_ONE_MINUS_SRC_ALPHA,
                    .dst_alpha_blendfactor = SDL_GPU_BLENDFACTOR_ONE_MINUS_SRC_ALPHA
                }
            }},
        },
        .vertex_input_state = (SDL_GPUVertexInputState){
            .num_vertex_buffers = 1,
            .vertex_buffer_descriptions = (SDL_GPUVertexBufferDescription[]){{
                .slot = 0,
                .input_rate = SDL_GPU_VERTEXINPUTRATE_VERTEX,
                .instance_step_rate = 0,
                .pitch = sizeof(PositionTextureVertex)
            }},
            .num_vertex_attributes = 2,
            .vertex_attributes = (SDL_GPUVertexAttribute[]){{
                .buffer_slot = 0,
                .format = SDL_GPU_VERTEXELEMENTFORMAT_FLOAT3,
                .location = 0,
                .offset = 0
            }, {
                .buffer_slot = 0,
                .format = SDL_GPU_VERTEXELEMENTFORMAT_FLOAT2,
                .location = 1,
                .offset = sizeof(float) * 3
            }}
        },
        .primitive_type = SDL_GPU_PRIMITIVETYPE_TRIANGLELIST,
        .vertex_shader = vertexShader,
        .fragment_shader = fragmentShader,
    };

    context->Pipeline = SDL_CreateGPUGraphicsPipeline(context->Device, &pipelineCreateInfo);
    if (context->Pipeline == NULL)
    {
        SDL_Log("Failed to create pipeline!");
        SDL_ReleaseGPUShader(context->Device, vertexShader);
        SDL_ReleaseGPUShader(context->Device, fragmentShader);
        return -1;
    }

    SDL_ReleaseGPUShader(context->Device, vertexShader);
    SDL_ReleaseGPUShader(context->Device, fragmentShader);
    return 0;
}

int CreateVertexBuffer(Context *context)
{
    // Create the GPU resources
    context->VertexBuffer = SDL_CreateGPUBuffer(
        context->Device,
        &(SDL_GPUBufferCreateInfo) {
            .usage = SDL_GPU_BUFFERUSAGE_VERTEX,
            .size = sizeof(PositionTextureVertex) * 4
        }
    );

    context->IndexBuffer = SDL_CreateGPUBuffer(
        context->Device,
        &(SDL_GPUBufferCreateInfo) {
            .usage = SDL_GPU_BUFFERUSAGE_INDEX,
            .size = sizeof(Uint16) * 6
        }
    );

    // Set up buffer data
    SDL_GPUTransferBuffer* bufferTransferBuffer = SDL_CreateGPUTransferBuffer(
        context->Device,
        &(SDL_GPUTransferBufferCreateInfo) {
            .usage = SDL_GPU_TRANSFERBUFFERUSAGE_UPLOAD,
            .size = (sizeof(PositionTextureVertex) * 4) + (sizeof(Uint16) * 6)
        }
    );

    PositionTextureVertex* transferData = SDL_MapGPUTransferBuffer(
        context->Device,
        bufferTransferBuffer,
        false
    );

    transferData[0] = (PositionTextureVertex){ -0.5f, -0.5f, 0, 0, 0 };
    transferData[1] = (PositionTextureVertex){  0.5f, -0.5f, 0, 1, 0 };
    transferData[2] = (PositionTextureVertex){  0.5f,  0.5f, 0, 1, 1 };
    transferData[3] = (PositionTextureVertex){ -0.5f,  0.5f, 0, 0, 1 };

    Uint16* indexData = (Uint16*) &transferData[4];
    indexData[0] = 0;
    indexData[1] = 1;
    indexData[2] = 2;
    indexData[3] = 0;
    indexData[4] = 2;
    indexData[5] = 3;

    SDL_UnmapGPUTransferBuffer(context->Device, bufferTransferBuffer);

    // Upload the transfer data to the GPU resources
    SDL_GPUCommandBuffer* uploadCmdBuf = SDL_AcquireGPUCommandBuffer(context->Device);
    SDL_GPUCopyPass* copyPass = SDL_BeginGPUCopyPass(uploadCmdBuf);

    SDL_UploadToGPUBuffer(
        copyPass,
        &(SDL_GPUTransferBufferLocation) {
            .transfer_buffer = bufferTransferBuffer,
            .offset = 0
        },
        &(SDL_GPUBufferRegion) {
            .buffer = context->VertexBuffer,
            .offset = 0,
            .size = sizeof(PositionTextureVertex) * 4
        },
        false
    );

    SDL_UploadToGPUBuffer(
        copyPass,
        &(SDL_GPUTransferBufferLocation) {
            .transfer_buffer = bufferTransferBuffer,
            .offset = sizeof(PositionTextureVertex) * 4
        },
        &(SDL_GPUBufferRegion) {
            .buffer = context->IndexBuffer,
            .offset = 0,
            .size = sizeof(Uint16) * 6
        },
        false
    );

    SDL_EndGPUCopyPass(copyPass);
    SDL_SubmitGPUCommandBuffer(uploadCmdBuf);
    SDL_ReleaseGPUTransferBuffer(context->Device, bufferTransferBuffer);

    return 0;
}

int CreateTexture(Context *context)
{
    // Load the image
    SDL_Surface *imageData = LoadImage("test_texture.bmp", 4);
    if (imageData == NULL)
    {
        SDL_Log("Could not load image data!");
        return -1;
    }

    context->Texture = SDL_CreateGPUTexture(context->Device, &(SDL_GPUTextureCreateInfo){
        .type = SDL_GPU_TEXTURETYPE_2D,
        .format = SDL_GPU_TEXTUREFORMAT_R8G8B8A8_UNORM,
        .width = imageData->w,
        .height = imageData->h,
        .layer_count_or_depth = 1,
        .num_levels = 1,
        .usage = SDL_GPU_TEXTUREUSAGE_SAMPLER
    });

    if (context->Texture == NULL)
    {
        SDL_Log("Failed to create texture! Error: %s", SDL_GetError());
        SDL_DestroySurface(imageData);
        return -1;
    }

    context->Sampler = SDL_CreateGPUSampler(context->Device, &(SDL_GPUSamplerCreateInfo){
        .min_filter = SDL_GPU_FILTER_NEAREST,
        .mag_filter = SDL_GPU_FILTER_NEAREST,
        .mipmap_mode = SDL_GPU_SAMPLERMIPMAPMODE_NEAREST,
        .address_mode_u = SDL_GPU_SAMPLERADDRESSMODE_CLAMP_TO_EDGE,
        .address_mode_v = SDL_GPU_SAMPLERADDRESSMODE_CLAMP_TO_EDGE,
        .address_mode_w = SDL_GPU_SAMPLERADDRESSMODE_CLAMP_TO_EDGE,
    });

    if (context->Sampler == NULL)
    {
        SDL_Log("Failed to create sampler! Error: %s", SDL_GetError());
        SDL_DestroySurface(imageData);
        return -1;
    }

    // Set up texture data
    SDL_GPUTransferBuffer* textureTransferBuffer = SDL_CreateGPUTransferBuffer(
        context->Device,
        &(SDL_GPUTransferBufferCreateInfo) {
            .usage = SDL_GPU_TRANSFERBUFFERUSAGE_UPLOAD,
            .size = imageData->w * imageData->h * 4
        }
    );

    if (textureTransferBuffer == NULL)
    {
        SDL_Log("Failed to create texture transfer buffer! Error: %s", SDL_GetError());
        SDL_DestroySurface(imageData);
        return -1;
    }

    Uint8* textureTransferPtr = SDL_MapGPUTransferBuffer(
        context->Device,
        textureTransferBuffer,
        false
    );
    
    if (textureTransferPtr == NULL)
    {
        SDL_Log("Failed to map texture transfer buffer! Error: %s", SDL_GetError());
        SDL_DestroySurface(imageData);
        SDL_ReleaseGPUTransferBuffer(context->Device, textureTransferBuffer);
        return -1;
    }

    SDL_memcpy(textureTransferPtr, imageData->pixels, imageData->w * imageData->h * 4);
    SDL_UnmapGPUTransferBuffer(context->Device, textureTransferBuffer);

    // Upload the transfer data to the GPU resources
    SDL_GPUCommandBuffer* uploadCmdBuf = SDL_AcquireGPUCommandBuffer(context->Device);
    if (uploadCmdBuf == NULL)
    {
        SDL_Log("Failed to acquire command buffer for texture upload! Error: %s", SDL_GetError());
        SDL_DestroySurface(imageData);
        SDL_ReleaseGPUTransferBuffer(context->Device, textureTransferBuffer);
        return -1;
    }

    SDL_GPUCopyPass* copyPass = SDL_BeginGPUCopyPass(uploadCmdBuf);

    SDL_UploadToGPUTexture(
        copyPass,
        &(SDL_GPUTextureTransferInfo) {
            .transfer_buffer = textureTransferBuffer,
            .offset = 0, /* Zeroes out the rest */
        },
        &(SDL_GPUTextureRegion){
            .texture = context->Texture,
            .w = imageData->w,
            .h = imageData->h,
            .d = 1
        },
        false
    );

    SDL_DestroySurface(imageData);
    SDL_EndGPUCopyPass(copyPass);
    SDL_SubmitGPUCommandBuffer(uploadCmdBuf);
    SDL_ReleaseGPUTransferBuffer(context->Device, textureTransferBuffer);

    return 0;
}

int init(Context *context)
{
    // Initialize SDL
    if (!SDL_Init(SDL_INIT_VIDEO))
    {
        SDL_Log("SDL initialization failed: %s", SDL_GetError());
        return -1;
    }

    context->Device = SDL_CreateGPUDevice(SDL_GPU_SHADERFORMAT_SPIRV | SDL_GPU_SHADERFORMAT_DXIL | SDL_GPU_SHADERFORMAT_MSL, false, NULL);
    if (context->Device == NULL)
    {
        SDL_Log("GPUCreateDevice failed: %s", SDL_GetError());
        return -1;
    }

    context->Window = SDL_CreateWindow("Textured Animated Quad with Uniforms", 640, 480, 0);
    if (context->Window == NULL)
    {
        SDL_Log("CreateWindow failed: %s", SDL_GetError());
        return -1;
    }

    if (!SDL_ClaimWindowForGPUDevice(context->Device, context->Window))
    {
        SDL_Log("GPUClaimWindow failed");
        return -1;
    }

    // Create pipeline, vertex buffer, and texture
    if (CreatePipeline(context) < 0)
    {
        return -1;
    }

    if (CreateVertexBuffer(context) < 0)
    {
        return -1;
    }

    if (CreateTexture(context) < 0)
    {
        return -1;
    }

    context->app_in_background = false;
    context->t = 0.0f;
    context->last_tick = SDL_GetTicks() / 1000.0f;  // Initialize timing
    context->delta_time = 0.0f;

    SDL_Log("Textured Animated Quad with Uniforms initialized successfully!");
    SDL_Log("This example demonstrates:");
    SDL_Log("- Vertex uniforms (transformation matrices)");
    SDL_Log("- Fragment uniforms (color multipliers)");
    SDL_Log("- Four animated quads with different rotations and colors");

    return 0;
}

SDL_AppResult SDL_AppInit(void **appstate, int argc, char *argv[])
{
    (void)argc; // Suppress unused parameter warning
    (void)argv; // Suppress unused parameter warning
    
    Context *context = SDL_malloc(sizeof(Context));
    *appstate = context;

    if (init(context) < 0)
        return SDL_APP_FAILURE;
    return SDL_APP_CONTINUE;
}

SDL_AppResult SDL_AppEvent(void *appstate, SDL_Event *event)
{
    Context *context = (Context *)appstate;
    if (event->type == SDL_EVENT_QUIT)
    {
        return SDL_APP_SUCCESS;
    }

    // Handle app going to background/foreground
    if (event->type == SDL_EVENT_WILL_ENTER_BACKGROUND)
    {
        SDL_Log("App entering background");
        context->app_in_background = true;
        return SDL_APP_CONTINUE;
    }

    if (event->type == SDL_EVENT_DID_ENTER_FOREGROUND)
    {
        SDL_Log("App entering foreground");
        context->app_in_background = false;
        return SDL_APP_CONTINUE;
    }
    return SDL_APP_CONTINUE;
}

int update(Context *context)
{
    context->t += context->delta_time;
    return 0;
}

int render(Context *context)
{
    SDL_GPUCommandBuffer *cmdbuf = SDL_AcquireGPUCommandBuffer(context->Device);
    if (cmdbuf == NULL)
    {
        SDL_Log("AcquireGPUCommandBuffer failed: %s", SDL_GetError());
        return -1;
    }

    SDL_GPUTexture *swapchainTexture;
    if (!SDL_WaitAndAcquireGPUSwapchainTexture(cmdbuf, context->Window, &swapchainTexture, NULL, NULL))
    {
        SDL_Log("WaitAndAcquireGPUSwapchainTexture failed: %s", SDL_GetError());
        return -1;
    }

    if (swapchainTexture != NULL)
    {
        SDL_GPUColorTargetInfo colorTargetInfo = { 0 };
        colorTargetInfo.texture = swapchainTexture;
        colorTargetInfo.clear_color = (SDL_FColor){ 0.0f, 0.0f, 0.0f, 1.0f };
        colorTargetInfo.load_op = SDL_GPU_LOADOP_CLEAR;
        colorTargetInfo.store_op = SDL_GPU_STOREOP_STORE;

        SDL_GPURenderPass* renderPass = SDL_BeginGPURenderPass(cmdbuf, &colorTargetInfo, 1, NULL);

        SDL_BindGPUGraphicsPipeline(renderPass, context->Pipeline);
        SDL_BindGPUVertexBuffers(renderPass, 0, &(SDL_GPUBufferBinding){ .buffer = context->VertexBuffer, .offset = 0 }, 1);
        SDL_BindGPUIndexBuffer(renderPass, &(SDL_GPUBufferBinding){ .buffer = context->IndexBuffer, .offset = 0 }, SDL_GPU_INDEXELEMENTSIZE_16BIT);
        SDL_BindGPUFragmentSamplers(renderPass, 0, &(SDL_GPUTextureSamplerBinding){ .texture = context->Texture, .sampler = context->Sampler }, 1);

        // Top-left quad - Red tint that pulses
        Matrix4x4 matrixUniform = Matrix4x4_Multiply(
            Matrix4x4_CreateRotationZ(context->t),
            Matrix4x4_CreateTranslation(-0.5f, -0.5f, 0)
        );
        SDL_PushGPUVertexUniformData(cmdbuf, 0, &matrixUniform, sizeof(matrixUniform));
        SDL_PushGPUFragmentUniformData(cmdbuf, 0, &(FragMultiplyUniform){
            0.5f + SDL_sinf(context->t) * 0.5f,  // Red channel pulses
            0.2f,                                 // Low green
            0.2f,                                 // Low blue
            1.0f
        }, sizeof(FragMultiplyUniform));
        SDL_DrawGPUIndexedPrimitives(renderPass, 6, 1, 0, 0, 0);

        // Top-right quad - Green tint that pulses
        matrixUniform = Matrix4x4_Multiply(
            Matrix4x4_CreateRotationZ((2.0f * SDL_PI_F) - context->t),
            Matrix4x4_CreateTranslation(0.5f, -0.5f, 0)
        );
        SDL_PushGPUVertexUniformData(cmdbuf, 0, &matrixUniform, sizeof(matrixUniform));
        SDL_PushGPUFragmentUniformData(cmdbuf, 0, &(FragMultiplyUniform){
            0.2f,                                 // Low red
            0.5f + SDL_cosf(context->t) * 0.5f,  // Green channel pulses
            0.2f,                                 // Low blue
            1.0f
        }, sizeof(FragMultiplyUniform));
        SDL_DrawGPUIndexedPrimitives(renderPass, 6, 1, 0, 0, 0);

        // Bottom-left quad - Blue tint that pulses slowly
        matrixUniform = Matrix4x4_Multiply(
            Matrix4x4_CreateRotationZ(context->t),
            Matrix4x4_CreateTranslation(-0.5f, 0.5f, 0)
        );
        SDL_PushGPUVertexUniformData(cmdbuf, 0, &matrixUniform, sizeof(matrixUniform));
        SDL_PushGPUFragmentUniformData(cmdbuf, 0, &(FragMultiplyUniform){
            0.2f,                                    // Low red
            0.2f,                                    // Low green
            0.5f + SDL_sinf(context->t * 0.5f) * 0.5f,  // Blue channel pulses slowly
            1.0f
        }, sizeof(FragMultiplyUniform));
        SDL_DrawGPUIndexedPrimitives(renderPass, 6, 1, 0, 0, 0);

        // Bottom-right quad - Purple/magenta tint that cycles
        matrixUniform = Matrix4x4_Multiply(
            Matrix4x4_CreateRotationZ(context->t),
            Matrix4x4_CreateTranslation(0.5f, 0.5f, 0)
        );
        SDL_PushGPUVertexUniformData(cmdbuf, 0, &matrixUniform, sizeof(matrixUniform));
        SDL_PushGPUFragmentUniformData(cmdbuf, 0, &(FragMultiplyUniform){
            0.5f + SDL_cosf(context->t) * 0.5f,  // Red channel cycles
            0.2f,                                 // Low green
            0.5f + SDL_sinf(context->t) * 0.5f,  // Blue channel cycles
            1.0f
        }, sizeof(FragMultiplyUniform));
        SDL_DrawGPUIndexedPrimitives(renderPass, 6, 1, 0, 0, 0);

        SDL_EndGPURenderPass(renderPass);
    }

    SDL_SubmitGPUCommandBuffer(cmdbuf);
    return 0;
}

SDL_AppResult SDL_AppIterate(void *appstate)
{
    Context *context = (Context *)appstate;
    if (context->app_in_background)
    {
        SDL_Delay(100); // Sleep to save battery
        return SDL_APP_CONTINUE;
    }

    // update
    float newTime = SDL_GetTicks() / 1000.0f;
    context->delta_time = newTime - context->last_tick;
    context->last_tick = newTime;

    if (update(context) < 0)
    {
        return SDL_APP_FAILURE;
    }

    // render
    if (render(context) < 0)
    {
        return SDL_APP_FAILURE;
    }

    return SDL_APP_CONTINUE;
}

void SDL_AppQuit(void *appstate, SDL_AppResult result)
{
    (void)result; // Suppress unused parameter warning
    
    Context *context = (Context *)appstate;

    // Clean up resources
    if (context->Pipeline)
    {
        SDL_ReleaseGPUGraphicsPipeline(context->Device, context->Pipeline);
    }
    if (context->VertexBuffer)
    {
        SDL_ReleaseGPUBuffer(context->Device, context->VertexBuffer);
    }
    if (context->IndexBuffer)
    {
        SDL_ReleaseGPUBuffer(context->Device, context->IndexBuffer);
    }
    if (context->Texture)
    {
        SDL_ReleaseGPUTexture(context->Device, context->Texture);
    }
    if (context->Sampler)
    {
        SDL_ReleaseGPUSampler(context->Device, context->Sampler);
    }

    SDL_ReleaseWindowFromGPUDevice(context->Device, context->Window);
    SDL_DestroyWindow(context->Window);
    SDL_DestroyGPUDevice(context->Device);

    SDL_free(context);
    SDL_Quit();
}
