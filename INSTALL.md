# To build and use SDL_ttf:

SDL_ttf supports a number of development environments:
- [CMake](docs/INTRO-cmake.md)
- [Visual Studio on Windows](docs/INTRO-visualstudio.md)
- [Xcode on Apple platforms](docs/INTRO-xcode.md)
- [Android Studio](docs/INTRO-androidstudio.md)
- [Emscripten for web](docs/INTRO-emscripten.md)

SDL_ttf is also usable in other environments. The basic steps are to use CMake to build the library and then use the headers and library that you built in your project. You can search online to see if anyone has specific steps for your setup.

# Documentation

An API reference and additional documentation is available at:

https://wiki.libsdl.org/SDL3_ttf

# Example code

There are simple example programs in the examples directory.

If you're using CMake, you can build them adding `-DSDLTTF_SAMPLES=ON` to the CMake command line when building SDL_ttf.

If you're using Visual Studio there are separate projects in the VisualC directory.

If you're using Xcode there are separate projects in the Xcode directory.

# Discussions

## Discord

You can join the official Discord server at:

https://discord.com/invite/BwpFGBWsv8

## Forums/mailing lists

You can join SDL development discussions at:

https://discourse.libsdl.org/

Once you sign up, you can use the forum through the website or as a mailing list from your email client.

## Announcement list

You can sign up for the low traffic announcement list at:

https://www.libsdl.org/mailing-list.php

