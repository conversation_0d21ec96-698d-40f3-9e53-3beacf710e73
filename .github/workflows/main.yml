name: Build

on: [push, pull_request]

jobs:
  Build:
    name: ${{ matrix.platform.name }}
    runs-on: ${{ matrix.platform.os }}

    defaults:
      run:
        shell: ${{ matrix.platform.shell }}

    strategy:
      fail-fast: false
      matrix:
        platform:
        - { name: Windows (MSVC+CMake),         os: windows-latest, shell: sh,   cmake: '-DPerl_ROOT=C:/Strawberry/perl/bin/ -DSDLTTF_VENDORED=ON -GNinja', msvc: 1, shared: 1, static: 0, artifact: 'SDL3_ttf-VC-x64' }
        - { name: Windows (mingw64+CMake),      os: windows-latest, shell: 'msys2 {0}', msystem: mingw64, msys-env: mingw-w64-x86_64, shared: 1, static: 0,
            cmake: '-DSDLTTF_VENDORED=OFF -G "Ninja Multi-Config"', artifact: 'SDL3_ttf-mingw64' }
        - { name: Linux,                        os: ubuntu-latest,  shell: sh,   cmake: '-DSDLTTF_VENDORED=ON -GNinja', shared: 1, static: 0, artifact: 'SDL3_image-linux-x64' }
        - { name: 'Linux (static)',             os: ubuntu-latest,  shell: sh,   cmake: '-GNinja -DBUILD_SHARED_LIBS=OFF', shared: 0, static: 1, artifact: 'SDL3_Image-static-linux-x64' }
        - { name: Macos,                        os: macos-latest,   shell: sh,   cmake: '-DSDLTTF_VENDORED=ON -GNinja', shared: 1, static: 0, artifact: 'SDL3_image-macos' }

    steps:
    - uses: ilammy/msvc-dev-cmd@v1
      if: ${{ matrix.platform.msvc }}
      with:
        arch: x64
    - name: Set up Ninja
      if: ${{ !contains(matrix.platform.shell, 'msys2') }}
      uses: aseprite/get-ninja@main
    - name: Set up MSYS2
      if: ${{ matrix.platform.shell == 'msys2 {0}' }}
      uses: msys2/setup-msys2@v2
      with:
        msystem: ${{ matrix.platform.msystem }}
        install: >-
          ${{ matrix.platform.msys-env }}-cmake
          ${{ matrix.platform.msys-env }}-gcc
          ${{ matrix.platform.msys-env }}-freetype
          ${{ matrix.platform.msys-env }}-harfbuzz
          ${{ matrix.platform.msys-env }}-ninja
          ${{ matrix.platform.msys-env }}-perl
          ${{ matrix.platform.msys-env }}-pkg-config
          ${{ matrix.platform.msys-env }}-zlib
    - name: Set up SDL
      id: sdl
      uses: libsdl-org/setup-sdl@main
      with:
        cmake-generator: Ninja
        version: 3-head
        sdl-test: true
        shell: ${{ matrix.platform.shell }}
    - name: Setup Macos dependencies
      if: ${{ runner.os == 'macOS' }}
      run: |
          brew install \
            freetype \
            harfbuzz \
            ${NULL+}
    - name: Set up Linux dependencies
      if: ${{ runner.os == 'Linux' }}
      run: |
        sudo apt-get update
        sudo apt-get -y install \
          cmake \
          file \
          fonts-dejavu-core \
          libfreetype-dev \
          libharfbuzz-dev \
          pkg-config \
          ${NULL+}
    - uses: actions/checkout@v4
      with:
        submodules: recursive

    - name: Check that versioning is consistent
      # We only need to run this once: arbitrarily use the Linux build
      if: ${{ runner.os == 'Linux' }}
      run: ./build-scripts/test-versioning.sh

    - name: Configure (CMake)
      run: |
        cmake -B build \
          -DBUILD_SHARED_LIBS=ON \
          -DSDLTTF_HARFBUZZ=ON \
          -DSDLTTF_SAMPLES=ON \
          -DSDLTTF_WERROR=ON \
          -DCMAKE_POSITION_INDEPENDENT_CODE=ON \
          -DCMAKE_BUILD_TYPE=Release \
          -DSDLTTF_INSTALL_MAN=ON \
          -DCMAKE_INSTALL_PREFIX=prefix_cmake \
          ${{ matrix.platform.cmake }}
    - name: Build (CMake)
      id: build
      run: |
        cmake --build build --config Release --verbose
    - name: Install (CMake)
      run: |
        set -eu
        rm -rf prefix_cmake
        cmake --install build/ --config Release --verbose
        echo "SDL3_ttf_ROOT=$(pwd)/prefix_cmake" >> $GITHUB_ENV
        ( cd prefix_cmake; find . ) | LC_ALL=C sort -u
    - name: Package (CPack)
      id: package
      if: ${{ always() && steps.build.outcome == 'success' }}
      run: |
        cmake --build build/ --target package
    - name: Test using showfont
      if: ${{ runner.os == 'Linux' }}
      run: |
          # Just check that it doesn't crash, we can't really test the results...
          env -C build/ SDL_VIDEODRIVER=dummy ./showfont --dump /usr/share/fonts/truetype/dejavu/DejaVuSans.ttf
          # ... but we can at least assert that it outputs a .bmp
          file build/glyph-100.bmp

    - name: Verify CMake configuration files
      run: |
          cmake -S cmake/test -B cmake_config_build \
            -DCMAKE_BUILD_TYPE=Release \
            -DTEST_SHARED=${{ matrix.platform.shared }} \
            -DTEST_STATIC=${{ matrix.platform.static }}
          cmake --build cmake_config_build --verbose --config Release
    - uses: actions/upload-artifact@v4
      if: ${{ always() && steps.package.outcome == 'success' }}
      with:
        if-no-files-found: error
        name: ${{ matrix.platform.artifact }}
        path: build/dist/SDL3_ttf*
