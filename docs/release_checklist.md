# Release checklist

## New feature release

* Update `CHANGES.txt`

* Bump version number to 3.EVEN.0 in all these locations:

    * `include/SDL3/SDL_ttf.h`:
        `SDL_TTF_MAJOR_VERSION`, `SDL_TTF_MINOR_VERSION`, `SDL_TTF_MICRO_VERSION`
    * `CMakeLists.txt`:
        `MAJOR_VERSION`, `MINOR_VERSION`, `MICRO_VERSION`
    * `version.rc`:
        `FILEVERSION`, `PRODUCTVERSION`, `FileVersion`, `ProductVersion`
    * `VisualC/Version.rc`:
        `FILEVERSION`, `PRODUCTVERSION`, `FileVersion`, `ProductVersion`
    * `Xcode/Info-Framework.plist`:
        `CFBundleShortVersionString`, `CFBundleVersion`

* Bump ABI version information

    * `Xcode/SDL_ttf.xcodeproj/project.pbxproj`:
        `DYLIB_CURRENT_VERSION`, `DYLIB_COMPATIBILITY_VERSION`
        * set first number in `DYLIB_CURRENT_VERSION` to
            (100 * *minor*) + 1
        * set second number in `DYLIB_CURRENT_VERSION` to 0
        * set `DYLIB_COMPATIBILITY_VERSION` to the same value

* Regenerate `configure`

* Run `./test-versioning.sh` to verify that everything is consistent

* Do the release

## New bugfix release

* Check that no new API/ABI was added

    * If it was, do a new feature release (see above) instead

* Bump version number from 3.Y.Z to 3.Y.(Z+1) (Y is even)

    * Same places as listed above

* Bump ABI version information

	* `Xcode/SDL_ttf.xcodeproj/project.pbxproj`:
	  `DYLIB_CURRENT_VERSION`, `DYLIB_COMPATIBILITY_VERSION`
		* set second number in `DYLIB_CURRENT_VERSION` to *micro*
        * Leave `DYLIB_COMPATIBILITY_VERSION` unchanged

* Regenerate `configure`

* Run test/versioning.sh to verify that everything is consistent

* Do the release

## After a feature release

* Create a branch like `release-3.6.x`

* Bump version number to 3.ODD.0 for next development branch

    * Same places as listed above

* Bump ABI version information

    * Same places as listed above
    * Assume that the next feature release will contain new API/ABI

* Run test/versioning.sh to verify that everything is consistent

* Add a new milestone for issues

## New development prerelease

* Bump version number from 3.Y.Z to 3.Y.(Z+1) (Y is odd)

    * Same places as listed above

* Bump ABI version information

	* `Xcode/SDL_ttf.xcodeproj/project.pbxproj`:
	  `DYLIB_CURRENT_VERSION`, `DYLIB_COMPATIBILITY_VERSION`
		* set first number in `DYLIB_CURRENT_VERSION` to
		  (100 * *minor*) + *micro* + 1
		* set second number in `DYLIB_CURRENT_VERSION` to 0
        * set `DYLIB_COMPATIBILITY_VERSION` to the same value

* Regenerate `configure`

* Run test/versioning.sh to verify that everything is consistent

* Do the release
