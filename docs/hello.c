/*
  Copyright (C) 1997-2025 Sam <PERSON> <<EMAIL>>

  This software is provided 'as-is', without any express or implied
  warranty.  In no event will the authors be held liable for any damages
  arising from the use of this software.

  Permission is granted to anyone to use this software for any purpose,
  including commercial applications, and to alter it and redistribute it
  freely.
*/
#define SDL_MAIN_USE_CALLBACKS 1  /* use the callbacks instead of main() */
#include <SDL3/SDL.h>
#include <SDL3/SDL_main.h>
#include <SDL3_ttf/SDL_ttf.h>

static SDL_Window *window = NULL;
static SDL_Renderer *renderer = NULL;
static SDL_Texture *texture = NULL;
static TTF_Font *font = NULL;

extern unsigned char tiny_ttf[];
extern unsigned int tiny_ttf_len;

/* This function runs once at startup. */
SDL_AppResult SDL_AppInit(void **appstate, int argc, char *argv[])
{
    SDL_Color color = { 255, 255, 255, SDL_ALPHA_OPAQUE };
    SDL_Surface *text;

    /* Create the window */
    if (!SDL_CreateWindowAndRenderer("Hello World", 800, 600, SDL_WINDOW_FULLSCREEN, &window, &renderer)) {
        SDL_Log("Couldn't create window and renderer: %s\n", SDL_GetError());
        return SDL_APP_FAILURE;
    }

    if (!TTF_Init()) {
        SDL_Log("Couldn't initialise SDL_ttf: %s\n", SDL_GetError());
        return SDL_APP_FAILURE;
    }

    /* Open the font */
    font = TTF_OpenFontIO(SDL_IOFromConstMem(tiny_ttf, tiny_ttf_len), true, 18.0f);
    if (!font) {
        SDL_Log("Couldn't open font: %s\n", SDL_GetError());
        return SDL_APP_FAILURE;
    }

    /* Create the text */
    text = TTF_RenderText_Blended(font, "Hello World!", 0, color);
    if (text) {
        texture = SDL_CreateTextureFromSurface(renderer, text);
        SDL_DestroySurface(text);
    }
    if (!texture) {
        SDL_Log("Couldn't create text: %s\n", SDL_GetError());
        return SDL_APP_FAILURE;
    }

    return SDL_APP_CONTINUE;
}

/* This function runs when a new event (mouse input, keypresses, etc) occurs. */
SDL_AppResult SDL_AppEvent(void *appstate, SDL_Event *event)
{
    if (event->type == SDL_EVENT_KEY_DOWN ||
        event->type == SDL_EVENT_QUIT) {
        return SDL_APP_SUCCESS;  /* end the program, reporting success to the OS. */
    }
    return SDL_APP_CONTINUE;
}

/* This function runs once per frame, and is the heart of the program. */
SDL_AppResult SDL_AppIterate(void *appstate)
{
    int w = 0, h = 0;
    SDL_FRect dst;
    const float scale = 4.0f;

    /* Center the text and scale it up */
    SDL_GetRenderOutputSize(renderer, &w, &h);
    SDL_SetRenderScale(renderer, scale, scale);
    SDL_GetTextureSize(texture, &dst.w, &dst.h);
    dst.x = ((w / scale) - dst.w) / 2;
    dst.y = ((h / scale) - dst.h) / 2;

    /* Draw the text */
    SDL_SetRenderDrawColor(renderer, 0, 0, 0, 255);
    SDL_RenderClear(renderer);
    SDL_RenderTexture(renderer, texture, NULL, &dst);
    SDL_RenderPresent(renderer);

    return SDL_APP_CONTINUE;
}

/* This function runs once at shutdown. */
void SDL_AppQuit(void *appstate, SDL_AppResult result)
{
    if (font) {
        TTF_CloseFont(font);
    }
    TTF_Quit();
}

/* Data used by this example */
/* tiny.txt
    Font: Tiny
    Created By: Matthew Welch
    E-Mail: <EMAIL>
    Web Address: http://www.squaregear.net/fonts/

    My fonts are all free.  You can use them for personal or commercial projects,
    and I ask for no money.  I would, however, love to hear from you.  If you use
    my fonts for something please e-mail me letting me know how you used it.  Send
    me a copy if you can or let me know where I can find your work. You are under
    no obligation to do this, I just like to see how my fonts get used.

    A license.txt file should have been included with this font, explaining the
    license under which it is made available. You can also read it at:

    http://www.squaregear.net/fonts/license.shtml

    About the font:

    Tiny is, I believe, the smallest possible font (in pixel size). It stands at
    a lofty four pixels tall (five if you count descenders), yet it still contains
    all the printable ASCII characters. Presented here in TrueType format, you can
    print it out at any size you want, in order to make it actually readable. If
    you do want to show it in all its four pixel glory I recommend you render it
    first at a larger size (9 point worked well for me) and then shrink it down.
*/
/* license.txt
    Copyright (C) 2004 by Matthew Welch

    Permission is hereby granted, free of charge, to any person obtaining a copy of
    this font software and associated documentation files (the "Software"), to deal
    in the Software without restriction, including without limitation the rights to
    use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies
    of the Software, and to permit persons to whom the Software is furnished to do
    so, subject to the following conditions:

    The above copyright notice and this permission notice shall be included in
    all copies or substantial portions of the Software.

    THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
    IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
    FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
    AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
    LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
    OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
    THE SOFTWARE.
*/
unsigned char tiny_ttf[] = {
  0x00, 0x01, 0x00, 0x00, 0x00, 0x0a, 0x00, 0x80, 0x00, 0x03, 0x00, 0x20,
  0x4f, 0x53, 0x2f, 0x32, 0x7a, 0x61, 0x7a, 0x16, 0x00, 0x00, 0x00, 0xac,
  0x00, 0x00, 0x00, 0x4e, 0x63, 0x6d, 0x61, 0x70, 0x07, 0x3b, 0x08, 0x5a,
  0x00, 0x00, 0x00, 0xfc, 0x00, 0x00, 0x01, 0xd2, 0x67, 0x6c, 0x79, 0x66,
  0x6b, 0x16, 0x56, 0x6e, 0x00, 0x00, 0x02, 0xd0, 0x00, 0x00, 0x11, 0xf4,
  0x68, 0x65, 0x61, 0x64, 0xdb, 0x9f, 0xe5, 0xd7, 0x00, 0x00, 0x14, 0xc4,
  0x00, 0x00, 0x00, 0x36, 0x68, 0x68, 0x65, 0x61, 0x07, 0xd2, 0x05, 0x14,
  0x00, 0x00, 0x14, 0xfc, 0x00, 0x00, 0x00, 0x24, 0x68, 0x6d, 0x74, 0x78,
  0x30, 0xb0, 0xff, 0xff, 0x00, 0x00, 0x15, 0x20, 0x00, 0x00, 0x01, 0x90,
  0x6c, 0x6f, 0x63, 0x61, 0xde, 0xd4, 0xda, 0x87, 0x00, 0x00, 0x16, 0xb0,
  0x00, 0x00, 0x00, 0xca, 0x6d, 0x61, 0x78, 0x70, 0x00, 0x6b, 0x00, 0x22,
  0x00, 0x00, 0x17, 0x7c, 0x00, 0x00, 0x00, 0x20, 0x6e, 0x61, 0x6d, 0x65,
  0x47, 0xfd, 0xed, 0x65, 0x00, 0x00, 0x17, 0x9c, 0x00, 0x00, 0x02, 0x3f,
  0x70, 0x6f, 0x73, 0x74, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x19, 0xdc,
  0x00, 0x00, 0x00, 0x24, 0x00, 0x00, 0x03, 0x0c, 0x01, 0x90, 0x00, 0x05,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x73, 0x66, 0x74, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 0x7f,
  0x03, 0xe8, 0xff, 0x38, 0x00, 0x00, 0x03, 0xe8, 0x00, 0xc8, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x02, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14,
  0x00, 0x03, 0x00, 0x01, 0x00, 0x00, 0x01, 0x1a, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0xb8, 0x00, 0x00,
  0x00, 0x22, 0x00, 0x20, 0x00, 0x04, 0x00, 0x02, 0x00, 0x20, 0x00, 0x29,
  0x00, 0x2a, 0x00, 0x2e, 0x00, 0x30, 0x00, 0x34, 0x00, 0x39, 0x00, 0x40,
  0x00, 0x57, 0x00, 0x5a, 0x00, 0x60, 0x00, 0x64, 0x00, 0x66, 0x00, 0x7a,
  0x00, 0x7e, 0x00, 0x7f, 0xff, 0xff, 0x00, 0x00, 0x00, 0x20, 0x00, 0x21,
  0x00, 0x2a, 0x00, 0x2b, 0x00, 0x2f, 0x00, 0x31, 0x00, 0x35, 0x00, 0x3a,
  0x00, 0x41, 0x00, 0x58, 0x00, 0x5b, 0x00, 0x61, 0x00, 0x65, 0x00, 0x67,
  0x00, 0x7b, 0x00, 0x7f, 0xff, 0xff, 0x00, 0x00, 0x00, 0x23, 0x00, 0x00,
  0x00, 0x23, 0x00, 0x00, 0x00, 0x08, 0x00, 0x09, 0x00, 0x00, 0xff, 0xdd,
  0x00, 0x00, 0xff, 0xff, 0xff, 0xa2, 0x00, 0x00, 0xff, 0xa3, 0xff, 0xe5,
  0xff, 0xd3, 0x00, 0x01, 0x00, 0x22, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00,
  0x00, 0x1e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x00, 0x26,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x26, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x09, 0x00, 0x35, 0x00, 0x53, 0x00, 0x43, 0x00, 0x54,
  0x00, 0x55, 0x00, 0x56, 0x00, 0x58, 0x00, 0x57, 0x00, 0x59, 0x00, 0x4d,
  0x00, 0x36, 0x00, 0x37, 0x00, 0x38, 0x00, 0x3d, 0x00, 0x08, 0x00, 0x00,
  0x00, 0x01, 0x00, 0x00, 0xff, 0x38, 0x02, 0x58, 0x03, 0xe8, 0x00, 0x03,
  0x00, 0x00, 0x05, 0x09, 0x02, 0x01, 0x2c, 0xfe, 0xd4, 0x01, 0x2c, 0x01,
  0x2c, 0xc8, 0x02, 0xbc, 0x01, 0xf4, 0xfe, 0x0c, 0x00, 0x02, 0x00, 0x00,
  0x00, 0x00, 0x02, 0x58, 0x02, 0x58, 0x00, 0x07, 0x00, 0x0b, 0x00, 0x00,
  0x33, 0x35, 0x33, 0x35, 0x23, 0x35, 0x21, 0x11, 0x01, 0x15, 0x23, 0x35,
  0xc8, 0xc8, 0xc8, 0x01, 0x90, 0xfe, 0x70, 0xc8, 0xc8, 0xc8, 0xc8, 0xfd,
  0xa8, 0x01, 0x90, 0xc8, 0xc8, 0x03, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00,
  0x02, 0x58, 0x03, 0x20, 0x00, 0x09, 0x00, 0x0d, 0x00, 0x00, 0x31, 0x11,
  0x33, 0x15, 0x33, 0x15, 0x23, 0x15, 0x33, 0x15, 0x11, 0x33, 0x15, 0x23,
  0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0x03, 0x20, 0xc8, 0xc8, 0xc8, 0xc8,
  0x01, 0x90, 0xc8, 0x4d, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x02, 0x58,
  0x02, 0x58, 0x00, 0x03, 0x00, 0x07, 0x00, 0x0b, 0x00, 0x00, 0x33, 0x35,
  0x21, 0x15, 0x01, 0x15, 0x23, 0x35, 0x29, 0x01, 0x35, 0x21, 0xc8, 0x01,
  0x90, 0xfe, 0x70, 0xc8, 0x02, 0x58, 0xfe, 0x70, 0x01, 0x90, 0xc8, 0xc8,
  0x01, 0x90, 0xc8, 0xc8, 0xc8, 0x5d, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00,
  0x02, 0x58, 0x03, 0x20, 0x00, 0x09, 0x00, 0x0d, 0x00, 0x00, 0x33, 0x35,
  0x33, 0x35, 0x23, 0x35, 0x33, 0x35, 0x33, 0x11, 0x01, 0x15, 0x23, 0x35,
  0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xfe, 0x70, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8,
  0xfc, 0xe0, 0x01, 0x90, 0xc8, 0xc8, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00,
  0x02, 0x58, 0x02, 0x58, 0x00, 0x0b, 0x00, 0x00, 0x33, 0x35, 0x23, 0x35,
  0x33, 0x35, 0x33, 0x15, 0x33, 0x15, 0x23, 0x15, 0xc8, 0xc8, 0xc8, 0xc8,
  0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0x00, 0x02, 0x00, 0x00,
  0x00, 0x00, 0x01, 0x90, 0x03, 0x20, 0x00, 0x07, 0x00, 0x0b, 0x00, 0x00,
  0x31, 0x11, 0x33, 0x15, 0x33, 0x15, 0x23, 0x15, 0x11, 0x35, 0x33, 0x15,
  0xc8, 0xc8, 0xc8, 0xc8, 0x02, 0x58, 0xc8, 0xc8, 0xc8, 0x02, 0x58, 0xc8,
  0xc8, 0x00, 0x00, 0x01, 0x00, 0x00, 0xff, 0x38, 0x01, 0x90, 0x02, 0x58,
  0x00, 0x07, 0x00, 0x00, 0x33, 0x35, 0x23, 0x11, 0x21, 0x11, 0x21, 0x35,
  0xc8, 0xc8, 0x01, 0x90, 0xfe, 0x70, 0xc8, 0x01, 0x90, 0xfc, 0xe0, 0xc8,
  0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x02, 0x58, 0x03, 0x20, 0x00, 0x07,
  0x00, 0x0b, 0x00, 0x00, 0x31, 0x11, 0x33, 0x15, 0x33, 0x15, 0x23, 0x11,
  0x13, 0x33, 0x11, 0x23, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0x03, 0x20,
  0xc8, 0xc8, 0xfe, 0x70, 0x01, 0x90, 0xfe, 0x70, 0x00, 0x02, 0x00, 0x00,
  0x00, 0x00, 0x00, 0xc8, 0x03, 0x20, 0x00, 0x03, 0x00, 0x07, 0x00, 0x00,
  0x31, 0x11, 0x33, 0x11, 0x03, 0x35, 0x33, 0x15, 0xc8, 0xc8, 0xc8, 0x01,
  0x90, 0xfe, 0x70, 0x02, 0x58, 0xc8, 0xc8, 0x01, 0x00, 0x02, 0x00, 0x00,
  0xff, 0x38, 0x00, 0xc8, 0x03, 0x20, 0x00, 0x03, 0x00, 0x07, 0x00, 0x00,
  0x15, 0x11, 0x33, 0x11, 0x03, 0x35, 0x33, 0x15, 0xc8, 0xc8, 0xc8, 0xc8,
  0x02, 0x58, 0xfd, 0xa8, 0x03, 0x20, 0xc8, 0xc8, 0x00, 0x03, 0x00, 0x00,
  0x00, 0x00, 0x02, 0x58, 0x03, 0x20, 0x00, 0x07, 0x00, 0x0b, 0x00, 0x0f,
  0x00, 0x00, 0x31, 0x11, 0x33, 0x11, 0x33, 0x15, 0x23, 0x15, 0x37, 0x33,
  0x15, 0x23, 0x11, 0x35, 0x33, 0x15, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8,
  0xc8, 0x03, 0x20, 0xfe, 0x70, 0xc8, 0xc8, 0xc8, 0xc8, 0x01, 0x90, 0xc8,
  0xc8, 0x08, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc8, 0x03, 0x20,
  0x00, 0x03, 0x00, 0x00, 0x31, 0x11, 0x33, 0x11, 0xc8, 0x03, 0x20, 0xfc,
  0xe0, 0x18, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x03, 0xe8, 0x02, 0x58,
  0x00, 0x05, 0x00, 0x09, 0x00, 0x0d, 0x00, 0x11, 0x00, 0x00, 0x31, 0x11,
  0x21, 0x15, 0x23, 0x11, 0x13, 0x33, 0x11, 0x23, 0x13, 0x35, 0x33, 0x15,
  0x19, 0x01, 0x33, 0x11, 0x01, 0x90, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8,
  0xc8, 0x02, 0x58, 0xc8, 0xfe, 0x70, 0x01, 0x90, 0xfe, 0x70, 0x01, 0x90,
  0xc8, 0xc8, 0xfe, 0x70, 0x01, 0x90, 0xfe, 0x70, 0x00, 0x02, 0x00, 0x00,
  0x00, 0x00, 0x02, 0x58, 0x02, 0x58, 0x00, 0x05, 0x00, 0x09, 0x00, 0x00,
  0x31, 0x11, 0x21, 0x15, 0x23, 0x11, 0x13, 0x33, 0x11, 0x23, 0x01, 0x90,
  0xc8, 0xc8, 0xc8, 0xc8, 0x02, 0x58, 0xc8, 0xfe, 0x70, 0x01, 0x90, 0xfe,
  0x70, 0x90, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x02, 0x58, 0x02, 0x58,
  0x00, 0x03, 0x00, 0x07, 0x00, 0x0b, 0x00, 0x0f, 0x00, 0x00, 0x33, 0x35,
  0x33, 0x15, 0x03, 0x15, 0x23, 0x35, 0x21, 0x23, 0x35, 0x33, 0x11, 0x35,
  0x33, 0x15, 0xc8, 0xc8, 0xc8, 0xc8, 0x01, 0x90, 0xc8, 0xc8, 0xc8, 0xc8,
  0xc8, 0x01, 0x90, 0xc8, 0xc8, 0xc8, 0xfe, 0x70, 0xc8, 0xc8, 0x00, 0x02,
  0x00, 0x00, 0xff, 0x38, 0x02, 0x58, 0x02, 0x58, 0x00, 0x09, 0x00, 0x0d,
  0x00, 0x00, 0x15, 0x11, 0x21, 0x15, 0x23, 0x15, 0x33, 0x15, 0x23, 0x15,
  0x13, 0x33, 0x15, 0x23, 0x01, 0x90, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8,
  0xc8, 0x03, 0x20, 0xc8, 0xc8, 0xc8, 0xc8, 0x02, 0x58, 0xc8, 0x00, 0x02,
  0x00, 0x00, 0xff, 0x38, 0x02, 0x58, 0x02, 0x58, 0x00, 0x09, 0x00, 0x0d,
  0x00, 0x00, 0x33, 0x35, 0x33, 0x35, 0x23, 0x35, 0x21, 0x11, 0x23, 0x35,
  0x03, 0x15, 0x23, 0x35, 0xc8, 0xc8, 0xc8, 0x01, 0x90, 0xc8, 0xc8, 0xc8,
  0xc8, 0xc8, 0xc8, 0xfc, 0xe0, 0xc8, 0x01, 0x90, 0xc8, 0xc8, 0x00, 0x01,
  0x00, 0x00, 0x00, 0x00, 0x01, 0x90, 0x02, 0x58, 0x00, 0x05, 0x00, 0x00,
  0x31, 0x11, 0x21, 0x15, 0x23, 0x11, 0x01, 0x90, 0xc8, 0x02, 0x58, 0xc8,
  0xfe, 0x70, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x02, 0x58, 0x02, 0x58,
  0x00, 0x07, 0x00, 0x00, 0x31, 0x35, 0x33, 0x11, 0x21, 0x15, 0x23, 0x11,
  0xc8, 0x01, 0x90, 0xc8, 0xc8, 0x01, 0x90, 0xc8, 0xfe, 0x70, 0x00, 0x01,
  0x00, 0x00, 0x00, 0x00, 0x02, 0x58, 0x03, 0x20, 0x00, 0x0b, 0x00, 0x00,
  0x33, 0x11, 0x23, 0x35, 0x33, 0x35, 0x33, 0x15, 0x33, 0x15, 0x23, 0x11,
  0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0x01, 0x90, 0xc8, 0xc8, 0xc8, 0xc8,
  0xfe, 0x70, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x02, 0x58, 0x02, 0x58,
  0x00, 0x05, 0x00, 0x09, 0x00, 0x00, 0x01, 0x11, 0x21, 0x35, 0x33, 0x11,
  0x03, 0x23, 0x11, 0x33, 0x02, 0x58, 0xfe, 0x70, 0xc8, 0xc8, 0xc8, 0xc8,
  0x02, 0x58, 0xfd, 0xa8, 0xc8, 0x01, 0x90, 0xfe, 0x70, 0x01, 0x90, 0x40,
  0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x02, 0x58, 0x02, 0x58, 0x00, 0x03,
  0x00, 0x07, 0x00, 0x0b, 0x00, 0x00, 0x33, 0x35, 0x33, 0x15, 0x03, 0x11,
  0x23, 0x11, 0x01, 0x11, 0x33, 0x11, 0xc8, 0xc8, 0xc8, 0xc8, 0x01, 0x90,
  0xc8, 0xc8, 0xc8, 0x02, 0x58, 0xfe, 0x70, 0x01, 0x90, 0xfe, 0x70, 0x01,
  0x90, 0xfe, 0x70, 0x00, 0x00, 0x05, 0x00, 0x00, 0x00, 0x00, 0x03, 0xe8,
  0x02, 0x58, 0x00, 0x03, 0x00, 0x07, 0x00, 0x0b, 0x00, 0x0f, 0x00, 0x13,
  0x00, 0x00, 0x33, 0x35, 0x33, 0x15, 0x03, 0x11, 0x23, 0x11, 0x01, 0x35,
  0x33, 0x1d, 0x01, 0x35, 0x33, 0x15, 0x35, 0x11, 0x33, 0x11, 0xc8, 0xc8,
  0xc8, 0xc8, 0x01, 0x90, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0x02, 0x58, 0xfe,
  0x70, 0x01, 0x90, 0xfe, 0x70, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0x01,
  0x90, 0xfe, 0x70, 0x04, 0x00, 0x05, 0x00, 0x00, 0x00, 0x00, 0x02, 0x58,
  0x02, 0x58, 0x00, 0x03, 0x00, 0x07, 0x00, 0x0b, 0x00, 0x0f, 0x00, 0x13,
  0x00, 0x00, 0x37, 0x35, 0x33, 0x1d, 0x01, 0x35, 0x33, 0x15, 0x03, 0x35,
  0x33, 0x15, 0x21, 0x35, 0x33, 0x15, 0x03, 0x35, 0x33, 0x15, 0xc8, 0xc8,
  0xc8, 0xc8, 0xc8, 0xfd, 0xa8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8,
  0xc8, 0xc8, 0x01, 0x90, 0xc8, 0xc8, 0xc8, 0xc8, 0xfe, 0x70, 0xc8, 0xc8,
  0x00, 0x02, 0x00, 0x00, 0xff, 0x38, 0x02, 0x58, 0x02, 0x58, 0x00, 0x09,
  0x00, 0x0d, 0x00, 0x00, 0x35, 0x11, 0x33, 0x15, 0x33, 0x35, 0x33, 0x11,
  0x23, 0x35, 0x1d, 0x01, 0x21, 0x35, 0xc8, 0xc8, 0xc8, 0xc8, 0xfe, 0x70,
  0xc8, 0x01, 0x90, 0xc8, 0xc8, 0xfd, 0xa8, 0xc8, 0xc8, 0xc8, 0xc8, 0x00,
  0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x02, 0x58, 0x02, 0x58, 0x00, 0x0b,
  0x00, 0x00, 0x31, 0x35, 0x33, 0x35, 0x23, 0x35, 0x21, 0x15, 0x23, 0x15,
  0x33, 0x15, 0xc8, 0xc8, 0x02, 0x58, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8,
  0xc8, 0xc8, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x02, 0x58, 0x03, 0x20,
  0x00, 0x0b, 0x00, 0x0f, 0x00, 0x00, 0x31, 0x11, 0x33, 0x15, 0x33, 0x35,
  0x33, 0x11, 0x23, 0x35, 0x23, 0x15, 0x11, 0x35, 0x33, 0x15, 0xc8, 0xc8,
  0xc8, 0xc8, 0xc8, 0xc8, 0x02, 0x58, 0xc8, 0xc8, 0xfd, 0xa8, 0xc8, 0xc8,
  0x02, 0x58, 0xc8, 0xc8, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x02, 0x58,
  0x03, 0x20, 0x00, 0x0b, 0x00, 0x00, 0x31, 0x11, 0x21, 0x15, 0x33, 0x11,
  0x23, 0x35, 0x23, 0x15, 0x33, 0x15, 0x01, 0x90, 0xc8, 0xc8, 0xc8, 0xc8,
  0x03, 0x20, 0xc8, 0xfe, 0x70, 0xc8, 0xc8, 0xc8, 0x00, 0x03, 0x00, 0x00,
  0x00, 0x00, 0x02, 0x58, 0x03, 0x20, 0x00, 0x03, 0x00, 0x07, 0x00, 0x0b,
  0x00, 0x00, 0x35, 0x11, 0x33, 0x19, 0x01, 0x35, 0x21, 0x15, 0x01, 0x35,
  0x21, 0x15, 0xc8, 0x01, 0x90, 0xfe, 0x70, 0x01, 0x90, 0xc8, 0x01, 0x90,
  0xfe, 0x70, 0x01, 0x90, 0xc8, 0xc8, 0xfd, 0xa8, 0xc8, 0xc8, 0x00, 0x02,
  0x00, 0x00, 0x00, 0x00, 0x02, 0x58, 0x03, 0x20, 0x00, 0x07, 0x00, 0x0b,
  0x00, 0x00, 0x31, 0x11, 0x21, 0x15, 0x23, 0x11, 0x33, 0x15, 0x35, 0x11,
  0x33, 0x11, 0x01, 0x90, 0xc8, 0xc8, 0xc8, 0x03, 0x20, 0xc8, 0xfe, 0x70,
  0xc8, 0xc8, 0x01, 0x90, 0xfe, 0x70, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00,
  0x02, 0x58, 0x03, 0x20, 0x00, 0x09, 0x00, 0x00, 0x31, 0x11, 0x21, 0x15,
  0x23, 0x15, 0x23, 0x15, 0x21, 0x15, 0x02, 0x58, 0xc8, 0xc8, 0x01, 0x90,
  0x03, 0x20, 0xc8, 0xc8, 0xc8, 0xc8, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00,
  0x02, 0x58, 0x03, 0x20, 0x00, 0x09, 0x00, 0x00, 0x31, 0x11, 0x21, 0x15,
  0x21, 0x15, 0x33, 0x15, 0x23, 0x15, 0x02, 0x58, 0xfe, 0x70, 0xc8, 0xc8,
  0x03, 0x20, 0xc8, 0xc8, 0xc8, 0xc8, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00,
  0x02, 0x58, 0x03, 0x20, 0x00, 0x03, 0x00, 0x07, 0x00, 0x0d, 0x00, 0x00,
  0x35, 0x11, 0x33, 0x19, 0x01, 0x35, 0x21, 0x15, 0x01, 0x35, 0x33, 0x35,
  0x33, 0x11, 0xc8, 0x01, 0x90, 0xfe, 0x70, 0xc8, 0xc8, 0xc8, 0x01, 0x90,
  0xfe, 0x70, 0x01, 0x90, 0xc8, 0xc8, 0xfd, 0xa8, 0xc8, 0xc8, 0xfe, 0x70,
  0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x02, 0x58, 0x03, 0x20, 0x00, 0x0b,
  0x00, 0x00, 0x31, 0x11, 0x33, 0x11, 0x33, 0x11, 0x33, 0x11, 0x23, 0x35,
  0x23, 0x15, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0x03, 0x20, 0xfe, 0x70, 0x01,
  0x90, 0xfc, 0xe0, 0xc8, 0xc8, 0xc8, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00,
  0x02, 0x58, 0x03, 0x20, 0x00, 0x0b, 0x00, 0x00, 0x31, 0x35, 0x33, 0x11,
  0x23, 0x35, 0x21, 0x15, 0x23, 0x11, 0x33, 0x15, 0xc8, 0xc8, 0x02, 0x58,
  0xc8, 0xc8, 0xc8, 0x01, 0x90, 0xc8, 0xc8, 0xfe, 0x70, 0xc8, 0x00, 0x03,
  0x00, 0x00, 0x00, 0x00, 0x02, 0x58, 0x03, 0x20, 0x00, 0x03, 0x00, 0x07,
  0x00, 0x0b, 0x00, 0x00, 0x33, 0x35, 0x33, 0x15, 0x27, 0x23, 0x35, 0x33,
  0x17, 0x11, 0x33, 0x11, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8,
  0xc8, 0xc8, 0xc8, 0xc8, 0x02, 0x58, 0xfd, 0xa8, 0x00, 0x03, 0x00, 0x00,
  0x00, 0x00, 0x03, 0x20, 0x03, 0x20, 0x00, 0x09, 0x00, 0x0d, 0x00, 0x11,
  0x00, 0x00, 0x31, 0x11, 0x33, 0x11, 0x33, 0x35, 0x33, 0x11, 0x21, 0x15,
  0x01, 0x35, 0x33, 0x15, 0x03, 0x33, 0x15, 0x23, 0xc8, 0xc8, 0xc8, 0xfe,
  0x70, 0x01, 0x90, 0xc8, 0xc8, 0xc8, 0xc8, 0x03, 0x20, 0xfe, 0x70, 0xc8,
  0xfe, 0x70, 0xc8, 0x02, 0x58, 0xc8, 0xc8, 0xfe, 0x70, 0xc8, 0x00, 0x01,
  0x00, 0x00, 0x00, 0x00, 0x02, 0x58, 0x03, 0x20, 0x00, 0x05, 0x00, 0x00,
  0x31, 0x11, 0x33, 0x11, 0x21, 0x15, 0xc8, 0x01, 0x90, 0x03, 0x20, 0xfd,
  0xa8, 0xc8, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x03, 0xe8, 0x03, 0x20,
  0x00, 0x07, 0x00, 0x0b, 0x00, 0x13, 0x00, 0x00, 0x31, 0x11, 0x33, 0x15,
  0x33, 0x15, 0x23, 0x11, 0x37, 0x35, 0x33, 0x15, 0x3d, 0x01, 0x33, 0x35,
  0x33, 0x11, 0x23, 0x11, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8,
  0x03, 0x20, 0xc8, 0xc8, 0xfe, 0x70, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8,
  0xfc, 0xe0, 0x01, 0x90, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x03, 0x20,
  0x03, 0x20, 0x00, 0x07, 0x00, 0x0f, 0x00, 0x00, 0x31, 0x11, 0x33, 0x15,
  0x33, 0x15, 0x23, 0x11, 0x37, 0x35, 0x33, 0x11, 0x33, 0x11, 0x23, 0x35,
  0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0x03, 0x20, 0xc8, 0xc8, 0xfe,
  0x70, 0xc8, 0xc8, 0x01, 0x90, 0xfc, 0xe0, 0xc8, 0x00, 0x04, 0x00, 0x00,
  0x00, 0x00, 0x03, 0x20, 0x03, 0x20, 0x00, 0x03, 0x00, 0x07, 0x00, 0x0b,
  0x00, 0x0f, 0x00, 0x00, 0x35, 0x11, 0x33, 0x19, 0x01, 0x35, 0x21, 0x15,
  0x01, 0x35, 0x21, 0x15, 0x11, 0x33, 0x11, 0x23, 0xc8, 0x01, 0x90, 0xfe,
  0x70, 0x01, 0x90, 0xc8, 0xc8, 0xc8, 0x01, 0x90, 0xfe, 0x70, 0x01, 0x90,
  0xc8, 0xc8, 0xfd, 0xa8, 0xc8, 0xc8, 0x02, 0x58, 0xfe, 0x70, 0x00, 0x02,
  0x00, 0x00, 0x00, 0x00, 0x02, 0x58, 0x03, 0x20, 0x00, 0x09, 0x00, 0x0d,
  0x00, 0x00, 0x31, 0x11, 0x21, 0x15, 0x23, 0x15, 0x33, 0x15, 0x23, 0x15,
  0x13, 0x35, 0x33, 0x15, 0x01, 0x90, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0x03,
  0x20, 0xc8, 0xc8, 0xc8, 0xc8, 0x01, 0x90, 0xc8, 0xc8, 0x00, 0x00, 0x04,
  0x00, 0x00, 0xff, 0x38, 0x03, 0x20, 0x03, 0x20, 0x00, 0x03, 0x00, 0x07,
  0x00, 0x0d, 0x00, 0x11, 0x00, 0x00, 0x35, 0x11, 0x33, 0x19, 0x01, 0x35,
  0x21, 0x15, 0x01, 0x35, 0x21, 0x11, 0x23, 0x35, 0x13, 0x33, 0x11, 0x23,
  0xc8, 0x01, 0x90, 0xfe, 0x70, 0x01, 0x90, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8,
  0x01, 0x90, 0xfe, 0x70, 0x01, 0x90, 0xc8, 0xc8, 0xfd, 0xa8, 0xc8, 0xfe,
  0x70, 0xc8, 0x02, 0x58, 0xfe, 0x70, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00,
  0x02, 0x58, 0x03, 0x20, 0x00, 0x09, 0x00, 0x0d, 0x00, 0x11, 0x00, 0x00,
  0x31, 0x11, 0x21, 0x15, 0x23, 0x15, 0x33, 0x15, 0x23, 0x15, 0x13, 0x35,
  0x33, 0x15, 0x07, 0x33, 0x15, 0x23, 0x01, 0x90, 0xc8, 0xc8, 0xc8, 0xc8,
  0xc8, 0xc8, 0xc8, 0xc8, 0x03, 0x20, 0xc8, 0xc8, 0xc8, 0xc8, 0x01, 0x90,
  0xc8, 0xc8, 0xc8, 0xc8, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x02, 0x58,
  0x03, 0x20, 0x00, 0x03, 0x00, 0x07, 0x00, 0x0b, 0x00, 0x0f, 0x00, 0x00,
  0x31, 0x35, 0x21, 0x15, 0x3d, 0x01, 0x33, 0x15, 0x25, 0x23, 0x35, 0x33,
  0x31, 0x35, 0x21, 0x15, 0x01, 0x90, 0xc8, 0xfe, 0x70, 0xc8, 0xc8, 0x01,
  0x90, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0x00, 0x01,
  0x00, 0x00, 0x00, 0x00, 0x02, 0x58, 0x03, 0x20, 0x00, 0x07, 0x00, 0x00,
  0x33, 0x11, 0x23, 0x35, 0x21, 0x15, 0x23, 0x11, 0xc8, 0xc8, 0x02, 0x58,
  0xc8, 0x02, 0x58, 0xc8, 0xc8, 0xfd, 0xa8, 0x01, 0x00, 0x03, 0x00, 0x00,
  0x00, 0x00, 0x03, 0x20, 0x03, 0x20, 0x00, 0x03, 0x00, 0x07, 0x00, 0x0b,
  0x00, 0x00, 0x35, 0x11, 0x33, 0x11, 0x15, 0x35, 0x21, 0x15, 0x11, 0x33,
  0x11, 0x23, 0xc8, 0x01, 0x90, 0xc8, 0xc8, 0xc8, 0x02, 0x58, 0xfd, 0xa8,
  0xc8, 0xc8, 0xc8, 0x03, 0x20, 0xfd, 0xa8, 0x90, 0x00, 0x05, 0x00, 0x00,
  0x00, 0x00, 0x03, 0xe8, 0x03, 0x20, 0x00, 0x03, 0x00, 0x07, 0x00, 0x0b,
  0x00, 0x0f, 0x00, 0x13, 0x00, 0x00, 0x11, 0x33, 0x11, 0x23, 0x17, 0x35,
  0x33, 0x15, 0x31, 0x33, 0x15, 0x23, 0x37, 0x35, 0x33, 0x15, 0x35, 0x11,
  0x33, 0x11, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0x03,
  0x20, 0xfe, 0x70, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0x01,
  0x90, 0xfe, 0x70, 0x01, 0x00, 0x05, 0x00, 0x00, 0x00, 0x00, 0x03, 0xe8,
  0x03, 0x20, 0x00, 0x03, 0x00, 0x07, 0x00, 0x0b, 0x00, 0x0f, 0x00, 0x13,
  0x00, 0x00, 0x33, 0x35, 0x33, 0x15, 0x03, 0x11, 0x23, 0x11, 0x01, 0x11,
  0x33, 0x11, 0x15, 0x35, 0x33, 0x15, 0x35, 0x11, 0x33, 0x11, 0xc8, 0xc8,
  0xc8, 0xc8, 0x01, 0x90, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0x03, 0x20, 0xfd,
  0xa8, 0x02, 0x58, 0xfd, 0xa8, 0x01, 0x90, 0xfe, 0x70, 0xc8, 0xc8, 0xc8,
  0xc8, 0x02, 0x58, 0xfd, 0xa8, 0x73, 0x00, 0x05, 0x00, 0x00, 0x00, 0xc8,
  0x02, 0x58, 0x03, 0x20, 0x00, 0x03, 0x00, 0x07, 0x00, 0x0b, 0x00, 0x0f,
  0x00, 0x13, 0x00, 0x00, 0x25, 0x35, 0x33, 0x15, 0x03, 0x35, 0x33, 0x15,
  0x01, 0x35, 0x33, 0x15, 0x3d, 0x01, 0x33, 0x15, 0x25, 0x35, 0x33, 0x15,
  0x01, 0x90, 0xc8, 0xc8, 0xc8, 0xfd, 0xa8, 0xc8, 0xc8, 0xfe, 0x70, 0xc8,
  0xc8, 0xc8, 0xc8, 0x01, 0x90, 0xc8, 0xc8, 0xfe, 0x70, 0xc8, 0xc8, 0xc8,
  0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0x00, 0x00, 0x05, 0x00, 0x00, 0x00, 0x00,
  0x02, 0x58, 0x03, 0x20, 0x00, 0x03, 0x00, 0x07, 0x00, 0x0b, 0x00, 0x0f,
  0x00, 0x13, 0x00, 0x00, 0x21, 0x11, 0x33, 0x11, 0x03, 0x35, 0x33, 0x15,
  0x01, 0x11, 0x33, 0x19, 0x01, 0x35, 0x33, 0x15, 0x25, 0x35, 0x33, 0x15,
  0x01, 0x90, 0xc8, 0xc8, 0xc8, 0xfd, 0xa8, 0xc8, 0xc8, 0xfe, 0x70, 0xc8,
  0x01, 0x90, 0xfe, 0x70, 0x02, 0x58, 0xc8, 0xc8, 0xfd, 0xa8, 0x01, 0x90,
  0xfe, 0x70, 0x01, 0x90, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0x00, 0x00, 0x05,
  0x00, 0x00, 0x00, 0x00, 0x03, 0xe8, 0x03, 0x20, 0x00, 0x03, 0x00, 0x07,
  0x00, 0x0b, 0x00, 0x0f, 0x00, 0x13, 0x00, 0x00, 0x11, 0x33, 0x15, 0x23,
  0x3b, 0x01, 0x15, 0x23, 0x3b, 0x01, 0x11, 0x23, 0x13, 0x35, 0x33, 0x15,
  0x3d, 0x01, 0x33, 0x15, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8,
  0xc8, 0xc8, 0xc8, 0x03, 0x20, 0xc8, 0xc8, 0xfe, 0x70, 0x01, 0x90, 0xc8,
  0xc8, 0xc8, 0xc8, 0xc8, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x03, 0x20,
  0x03, 0x20, 0x00, 0x07, 0x00, 0x0f, 0x00, 0x00, 0x11, 0x21, 0x15, 0x23,
  0x15, 0x23, 0x35, 0x21, 0x05, 0x15, 0x21, 0x15, 0x21, 0x35, 0x33, 0x35,
  0x03, 0x20, 0xc8, 0xc8, 0xfe, 0x70, 0x01, 0x90, 0x01, 0x90, 0xfc, 0xe0,
  0xc8, 0x03, 0x20, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xff,
  0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x02, 0x58, 0x03, 0x20, 0x00, 0x0b,
  0x00, 0x00, 0x31, 0x35, 0x33, 0x35, 0x23, 0x35, 0x33, 0x35, 0x33, 0x11,
  0x33, 0x15, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xfd,
  0xa8, 0xc8, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x02, 0x58, 0x03, 0x20,
  0x00, 0x05, 0x00, 0x09, 0x00, 0x0d, 0x00, 0x00, 0x31, 0x11, 0x33, 0x15,
  0x21, 0x15, 0x01, 0x21, 0x15, 0x29, 0x01, 0x33, 0x15, 0x23, 0xc8, 0x01,
  0x90, 0xfd, 0xa8, 0x01, 0x90, 0xfe, 0x70, 0x01, 0x90, 0xc8, 0xc8, 0x01,
  0x90, 0xc8, 0xc8, 0x03, 0x20, 0xc8, 0xc8, 0x00, 0x00, 0x02, 0x00, 0x00,
  0x00, 0x00, 0x02, 0x58, 0x03, 0x20, 0x00, 0x03, 0x00, 0x0d, 0x00, 0x00,
  0x31, 0x35, 0x21, 0x15, 0x01, 0x21, 0x15, 0x33, 0x11, 0x23, 0x35, 0x23,
  0x35, 0x23, 0x01, 0x90, 0xfe, 0x70, 0x01, 0x90, 0xc8, 0xc8, 0xc8, 0xc8,
  0xc8, 0xc8, 0x03, 0x20, 0xc8, 0xfe, 0x70, 0xc8, 0xc8, 0x03, 0x00, 0x01,
  0x00, 0x00, 0x00, 0x00, 0x02, 0x58, 0x03, 0x20, 0x00, 0x09, 0x00, 0x00,
  0x21, 0x35, 0x21, 0x11, 0x33, 0x15, 0x33, 0x11, 0x33, 0x11, 0x01, 0x90,
  0xfe, 0x70, 0xc8, 0xc8, 0xc8, 0xc8, 0x01, 0x90, 0xc8, 0x01, 0x90, 0xfc,
  0xe0, 0x01, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x02, 0x58, 0x02, 0x58,
  0x00, 0x0b, 0x00, 0x00, 0x33, 0x35, 0x23, 0x35, 0x33, 0x35, 0x21, 0x15,
  0x23, 0x15, 0x33, 0x15, 0xc8, 0xc8, 0xc8, 0x01, 0x90, 0xc8, 0xc8, 0xc8,
  0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0x70, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00,
  0x02, 0x58, 0x03, 0x20, 0x00, 0x03, 0x00, 0x07, 0x00, 0x0d, 0x00, 0x00,
  0x31, 0x35, 0x21, 0x15, 0x3d, 0x01, 0x33, 0x15, 0x25, 0x23, 0x11, 0x21,
  0x15, 0x21, 0x01, 0x90, 0xc8, 0xfe, 0x70, 0xc8, 0x02, 0x58, 0xfe, 0x70,
  0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0x01, 0x90, 0xc8, 0x6f, 0x00, 0x02,
  0x00, 0x00, 0x00, 0x00, 0x02, 0x58, 0x03, 0x20, 0x00, 0x05, 0x00, 0x09,
  0x00, 0x00, 0x31, 0x11, 0x33, 0x15, 0x21, 0x11, 0x01, 0x35, 0x21, 0x15,
  0xc8, 0x01, 0x90, 0xfe, 0x70, 0x01, 0x90, 0x02, 0x58, 0xc8, 0xfe, 0x70,
  0x02, 0x58, 0xc8, 0xc8, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x02, 0x58,
  0x03, 0x20, 0x00, 0x05, 0x00, 0x09, 0x00, 0x0d, 0x00, 0x00, 0x11, 0x35,
  0x21, 0x11, 0x23, 0x35, 0x03, 0x35, 0x33, 0x15, 0x05, 0x35, 0x33, 0x15,
  0x02, 0x58, 0xc8, 0xc8, 0xc8, 0xfe, 0x70, 0xc8, 0x02, 0x58, 0xc8, 0xfe,
  0x70, 0xc8, 0xfe, 0x70, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0x79, 0x00, 0x02,
  0x00, 0x00, 0x00, 0x00, 0x02, 0x58, 0x03, 0x20, 0x00, 0x03, 0x00, 0x0f,
  0x00, 0x00, 0x37, 0x33, 0x15, 0x23, 0x13, 0x23, 0x15, 0x23, 0x11, 0x33,
  0x35, 0x33, 0x15, 0x33, 0x11, 0x23, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8,
  0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0x01, 0x90, 0xc8, 0x01, 0x90, 0xc8,
  0xc8, 0xfe, 0x70, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x02, 0x58,
  0x03, 0x20, 0x00, 0x0b, 0x00, 0x00, 0x21, 0x35, 0x21, 0x11, 0x33, 0x15,
  0x33, 0x35, 0x23, 0x35, 0x21, 0x11, 0x01, 0x90, 0xfe, 0x70, 0xc8, 0xc8,
  0xc8, 0x01, 0x90, 0xc8, 0x01, 0x90, 0xc8, 0xc8, 0xc8, 0xfc, 0xe0, 0x21,
  0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x02, 0x58, 0x03, 0x20, 0x00, 0x03,
  0x00, 0x07, 0x00, 0x0b, 0x00, 0x0f, 0x00, 0x00, 0x35, 0x11, 0x33, 0x19,
  0x01, 0x35, 0x33, 0x15, 0x03, 0x35, 0x33, 0x15, 0x11, 0x33, 0x11, 0x23,
  0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0x01, 0x90, 0xfe, 0x70, 0x01,
  0x90, 0xc8, 0xc8, 0xfd, 0xa8, 0xc8, 0xc8, 0x02, 0x58, 0xfe, 0x70, 0x58,
  0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc8, 0x03, 0x20, 0x00, 0x03,
  0x00, 0x07, 0x00, 0x00, 0x11, 0x33, 0x11, 0x23, 0x11, 0x35, 0x33, 0x15,
  0xc8, 0xc8, 0xc8, 0x03, 0x20, 0xfe, 0x70, 0xfe, 0x70, 0xc8, 0xc8, 0xc8,
  0x00, 0x02, 0x00, 0x00, 0x01, 0x90, 0x02, 0x58, 0x03, 0x20, 0x00, 0x03,
  0x00, 0x07, 0x00, 0x00, 0x13, 0x11, 0x23, 0x11, 0x01, 0x11, 0x33, 0x11,
  0xc8, 0xc8, 0x01, 0x90, 0xc8, 0x03, 0x20, 0xfe, 0x70, 0x01, 0x90, 0xfe,
  0x70, 0x01, 0x90, 0xfe, 0x70, 0xfe, 0x00, 0x02, 0x00, 0x00, 0xff, 0x38,
  0x03, 0xe8, 0x03, 0x20, 0x00, 0x1b, 0x00, 0x1f, 0x00, 0x00, 0x11, 0x35,
  0x33, 0x35, 0x33, 0x15, 0x33, 0x35, 0x33, 0x15, 0x33, 0x15, 0x23, 0x15,
  0x33, 0x15, 0x23, 0x15, 0x23, 0x35, 0x23, 0x15, 0x23, 0x35, 0x23, 0x35,
  0x33, 0x35, 0x17, 0x33, 0x35, 0x23, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8,
  0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0x01, 0x90,
  0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8,
  0xc8, 0xc8, 0xc8, 0xc8, 0x00, 0x01, 0x00, 0x00, 0xff, 0x38, 0x02, 0x58,
  0x03, 0x20, 0x00, 0x13, 0x00, 0x00, 0x11, 0x35, 0x33, 0x35, 0x33, 0x15,
  0x33, 0x15, 0x23, 0x15, 0x33, 0x15, 0x23, 0x15, 0x23, 0x35, 0x23, 0x35,
  0x33, 0x35, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0x01,
  0x90, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8,
  0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x03, 0x20, 0x03, 0x20, 0x00, 0x03,
  0x00, 0x07, 0x00, 0x0b, 0x00, 0x0f, 0x00, 0x13, 0x00, 0x17, 0x00, 0x00,
  0x37, 0x35, 0x33, 0x15, 0x01, 0x35, 0x33, 0x15, 0x17, 0x35, 0x33, 0x15,
  0x11, 0x35, 0x33, 0x15, 0x21, 0x35, 0x33, 0x15, 0x01, 0x23, 0x35, 0x33,
  0xc8, 0xc8, 0xfe, 0x70, 0xc8, 0xc8, 0xc8, 0xc8, 0xfc, 0xe0, 0xc8, 0x02,
  0x58, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0x01, 0x90, 0xc8, 0xc8, 0xc8, 0xc8,
  0xc8, 0xfe, 0x70, 0xc8, 0xc8, 0xc8, 0xc8, 0x02, 0x58, 0xc8, 0x00, 0x04,
  0x00, 0x00, 0x00, 0x00, 0x03, 0x20, 0x03, 0x20, 0x00, 0x03, 0x00, 0x07,
  0x00, 0x0b, 0x00, 0x11, 0x00, 0x00, 0x33, 0x35, 0x21, 0x15, 0x3d, 0x01,
  0x33, 0x15, 0x21, 0x23, 0x35, 0x33, 0x31, 0x11, 0x21, 0x15, 0x23, 0x15,
  0xc8, 0x01, 0x90, 0xc8, 0xfd, 0xa8, 0xc8, 0xc8, 0x01, 0x90, 0xc8, 0xc8,
  0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0x01, 0x90, 0xc8, 0xc8, 0x74, 0x00, 0x01,
  0x00, 0x00, 0x01, 0x90, 0x00, 0xc8, 0x03, 0x20, 0x00, 0x03, 0x00, 0x00,
  0x13, 0x11, 0x23, 0x11, 0xc8, 0xc8, 0x03, 0x20, 0xfe, 0x70, 0x01, 0x90,
  0x00, 0x03, 0x00, 0x00, 0xff, 0x38, 0x01, 0x90, 0x03, 0x20, 0x00, 0x03,
  0x00, 0x07, 0x00, 0x0b, 0x00, 0x00, 0x31, 0x11, 0x33, 0x11, 0x13, 0x23,
  0x35, 0x33, 0x11, 0x23, 0x35, 0x33, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8,
  0x02, 0x58, 0xfd, 0xa8, 0x02, 0x58, 0xc8, 0xfc, 0x18, 0xc8, 0x00, 0x03,
  0x00, 0x00, 0xff, 0x38, 0x01, 0x90, 0x03, 0x20, 0x00, 0x03, 0x00, 0x07,
  0x00, 0x0b, 0x00, 0x00, 0x33, 0x11, 0x33, 0x11, 0x03, 0x23, 0x35, 0x33,
  0x11, 0x23, 0x35, 0x33, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0x02,
  0x58, 0xfd, 0xa8, 0x02, 0x58, 0xc8, 0xfc, 0x18, 0xc8, 0xc8, 0x00, 0x03,
  0x00, 0x00, 0x00, 0x00, 0x02, 0x58, 0x03, 0x20, 0x00, 0x03, 0x00, 0x09,
  0x00, 0x0d, 0x00, 0x00, 0x35, 0x11, 0x33, 0x19, 0x01, 0x35, 0x21, 0x11,
  0x23, 0x35, 0x03, 0x35, 0x21, 0x15, 0xc8, 0x01, 0x90, 0xc8, 0xc8, 0x01,
  0x90, 0xc8, 0x01, 0x90, 0xfe, 0x70, 0x01, 0x90, 0xc8, 0xfe, 0x70, 0xc8,
  0xfd, 0xa8, 0xc8, 0xc8, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x02, 0x58,
  0x02, 0x58, 0x00, 0x0b, 0x00, 0x00, 0x33, 0x35, 0x23, 0x35, 0x33, 0x35,
  0x33, 0x15, 0x33, 0x15, 0x23, 0x15, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8,
  0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0x00, 0x01, 0x00, 0x00, 0xff, 0x38,
  0x00, 0xc8, 0x00, 0xc8, 0x00, 0x03, 0x00, 0x00, 0x17, 0x23, 0x11, 0x33,
  0xc8, 0xc8, 0xc8, 0xc8, 0x01, 0x90, 0x00, 0x01, 0x00, 0x00, 0x00, 0xc8,
  0x02, 0x58, 0x01, 0x90, 0x00, 0x03, 0x00, 0x00, 0x3d, 0x01, 0x21, 0x15,
  0x02, 0x58, 0xc8, 0xc8, 0xc8, 0x15, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00,
  0x00, 0xc8, 0x00, 0xc8, 0x00, 0x03, 0x00, 0x00, 0x31, 0x35, 0x33, 0x15,
  0xc8, 0xc8, 0xc8, 0x11, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x02, 0x58,
  0x03, 0x20, 0x00, 0x03, 0x00, 0x07, 0x00, 0x0b, 0x00, 0x00, 0x37, 0x11,
  0x33, 0x11, 0x13, 0x23, 0x35, 0x33, 0x01, 0x23, 0x35, 0x33, 0xc8, 0xc8,
  0xc8, 0xc8, 0xc8, 0xfe, 0x70, 0xc8, 0xc8, 0xc8, 0x01, 0x90, 0xfe, 0x70,
  0x01, 0x90, 0xc8, 0xfc, 0xe0, 0xc8, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00,
  0x03, 0x20, 0x03, 0x20, 0x00, 0x03, 0x00, 0x07, 0x00, 0x0b, 0x00, 0x0f,
  0x00, 0x00, 0x37, 0x35, 0x33, 0x15, 0x3d, 0x01, 0x33, 0x15, 0x01, 0x35,
  0x33, 0x15, 0x01, 0x23, 0x35, 0x33, 0xc8, 0xc8, 0xc8, 0xfd, 0xa8, 0xc8,
  0x02, 0x58, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xfe, 0x70,
  0xc8, 0xc8, 0x02, 0x58, 0xc8, 0x02, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00,
  0x00, 0xc8, 0x02, 0x58, 0x00, 0x03, 0x00, 0x07, 0x00, 0x00, 0x31, 0x35,
  0x33, 0x15, 0x11, 0x23, 0x35, 0x33, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0x01,
  0x90, 0xc8, 0x00, 0x02, 0x00, 0x00, 0xff, 0x38, 0x00, 0xc8, 0x02, 0x58,
  0x00, 0x03, 0x00, 0x07, 0x00, 0x00, 0x17, 0x23, 0x11, 0x33, 0x35, 0x23,
  0x35, 0x33, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0x01, 0x90, 0xc8, 0xc8,
  0x00, 0x05, 0x00, 0x00, 0xff, 0x38, 0x02, 0x58, 0x03, 0x20, 0x00, 0x03,
  0x00, 0x07, 0x00, 0x0b, 0x00, 0x0f, 0x00, 0x13, 0x00, 0x00, 0x37, 0x23,
  0x35, 0x33, 0x31, 0x35, 0x33, 0x15, 0x3d, 0x01, 0x33, 0x15, 0x01, 0x35,
  0x33, 0x1d, 0x01, 0x35, 0x33, 0x15, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xfe,
  0x70, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xfd, 0xa8,
  0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0x9c, 0x00, 0x05, 0x00, 0x00, 0xff, 0x38,
  0x02, 0x58, 0x03, 0x20, 0x00, 0x03, 0x00, 0x07, 0x00, 0x0b, 0x00, 0x0f,
  0x00, 0x13, 0x00, 0x00, 0x25, 0x23, 0x35, 0x33, 0x21, 0x35, 0x33, 0x15,
  0x25, 0x35, 0x33, 0x15, 0x11, 0x35, 0x33, 0x15, 0x05, 0x35, 0x33, 0x15,
  0x02, 0x58, 0xc8, 0xc8, 0xfe, 0x70, 0xc8, 0xfe, 0x70, 0xc8, 0xc8, 0xfe,
  0x70, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xfd, 0xa8, 0xc8,
  0xc8, 0xc8, 0xc8, 0xc8, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x02, 0x58,
  0x02, 0x58, 0x00, 0x03, 0x00, 0x07, 0x00, 0x00, 0x31, 0x35, 0x21, 0x15,
  0x01, 0x35, 0x21, 0x15, 0x02, 0x58, 0xfd, 0xa8, 0x02, 0x58, 0xc8, 0xc8,
  0x01, 0x90, 0xc8, 0xc8, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x02, 0x58,
  0x03, 0x20, 0x00, 0x03, 0x00, 0x07, 0x00, 0x0b, 0x00, 0x00, 0x11, 0x35,
  0x21, 0x15, 0x31, 0x33, 0x15, 0x23, 0x03, 0x35, 0x33, 0x15, 0x01, 0x90,
  0xc8, 0xc8, 0xc8, 0xc8, 0x02, 0x58, 0xc8, 0xc8, 0xc8, 0xfe, 0x70, 0xc8,
  0xc8, 0x00, 0x00, 0x01, 0x00, 0x00, 0xff, 0x38, 0x01, 0x90, 0x03, 0x20,
  0x00, 0x07, 0x00, 0x00, 0x15, 0x11, 0x21, 0x15, 0x23, 0x11, 0x33, 0x15,
  0x01, 0x90, 0xc8, 0xc8, 0xc8, 0x03, 0xe8, 0xc8, 0xfd, 0xa8, 0xc8, 0x00,
  0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x03, 0x20, 0x03, 0x20, 0x00, 0x03,
  0x00, 0x07, 0x00, 0x0b, 0x00, 0x0f, 0x00, 0x00, 0x11, 0x35, 0x33, 0x15,
  0x13, 0x35, 0x33, 0x15, 0x01, 0x33, 0x15, 0x23, 0x05, 0x33, 0x15, 0x23,
  0xc8, 0xc8, 0xc8, 0xfe, 0x70, 0xc8, 0xc8, 0x01, 0x90, 0xc8, 0xc8, 0x02,
  0x58, 0xc8, 0xc8, 0xfe, 0x70, 0xc8, 0xc8, 0x01, 0x90, 0xc8, 0xc8, 0xc8,
  0x00, 0x01, 0x00, 0x00, 0xff, 0x38, 0x01, 0x90, 0x03, 0x20, 0x00, 0x07,
  0x00, 0x00, 0x15, 0x35, 0x33, 0x11, 0x23, 0x35, 0x21, 0x11, 0xc8, 0xc8,
  0x01, 0x90, 0xc8, 0xc8, 0x02, 0x58, 0xc8, 0xfc, 0x18, 0x01, 0x00, 0x03,
  0x00, 0x00, 0x01, 0x90, 0x02, 0x58, 0x03, 0x20, 0x00, 0x03, 0x00, 0x07,
  0x00, 0x0b, 0x00, 0x00, 0x11, 0x35, 0x33, 0x15, 0x3d, 0x01, 0x33, 0x15,
  0x31, 0x33, 0x15, 0x23, 0xc8, 0xc8, 0xc8, 0xc8, 0x01, 0x90, 0xc8, 0xc8,
  0xc8, 0xc8, 0xc8, 0xc8, 0x00, 0x01, 0x00, 0x00, 0xff, 0x38, 0x02, 0x58,
  0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x31, 0x21, 0x15, 0x21, 0x02, 0x58,
  0xfd, 0xa8, 0xc8, 0x00, 0x00, 0x02, 0x00, 0x00, 0x01, 0x90, 0x01, 0x90,
  0x03, 0x20, 0x00, 0x03, 0x00, 0x07, 0x00, 0x00, 0x11, 0x35, 0x33, 0x15,
  0x31, 0x33, 0x15, 0x23, 0xc8, 0xc8, 0xc8, 0x02, 0x58, 0xc8, 0xc8, 0xc8,
  0x00, 0x01, 0x00, 0x00, 0xff, 0x38, 0x02, 0x58, 0x03, 0x20, 0x00, 0x0b,
  0x00, 0x00, 0x3d, 0x01, 0x33, 0x11, 0x21, 0x15, 0x23, 0x11, 0x33, 0x15,
  0x21, 0x11, 0xc8, 0x01, 0x90, 0xc8, 0xc8, 0xfe, 0x70, 0xc8, 0xc8, 0x01,
  0x90, 0xc8, 0xfd, 0xa8, 0xc8, 0x01, 0x90, 0x00, 0x00, 0x01, 0x00, 0x00,
  0xff, 0x38, 0x00, 0xc8, 0x03, 0x20, 0x00, 0x03, 0x00, 0x00, 0x15, 0x11,
  0x33, 0x11, 0xc8, 0xc8, 0x03, 0xe8, 0xfc, 0x18, 0x00, 0x01, 0x00, 0x00,
  0xff, 0x38, 0x02, 0x58, 0x03, 0x20, 0x00, 0x0b, 0x00, 0x00, 0x15, 0x35,
  0x33, 0x11, 0x23, 0x35, 0x21, 0x11, 0x33, 0x15, 0x23, 0x11, 0xc8, 0xc8,
  0x01, 0x90, 0xc8, 0xc8, 0xc8, 0xc8, 0x02, 0x58, 0xc8, 0xfe, 0x70, 0xc8,
  0xfe, 0x70, 0x00, 0x04, 0x00, 0x00, 0x01, 0x90, 0x03, 0x20, 0x03, 0x20,
  0x00, 0x03, 0x00, 0x07, 0x00, 0x0b, 0x00, 0x0f, 0x00, 0x00, 0x11, 0x35,
  0x33, 0x15, 0x25, 0x35, 0x33, 0x15, 0x21, 0x33, 0x15, 0x23, 0x27, 0x35,
  0x33, 0x15, 0xc8, 0x01, 0x90, 0xc8, 0xfe, 0x70, 0xc8, 0xc8, 0xc8, 0xc8,
  0x01, 0x90, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0xc8, 0x00,
  0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x55, 0x6a, 0xd9, 0x38,
  0x5f, 0x0f, 0x3c, 0xf5, 0x00, 0x00, 0x04, 0xb0, 0x00, 0x00, 0x00, 0x00,
  0xbc, 0x53, 0x50, 0x89, 0x00, 0x00, 0x00, 0x00, 0xbc, 0x53, 0x50, 0x89,
  0x00, 0x00, 0xff, 0x38, 0x03, 0xe8, 0x03, 0xe8, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00,
  0x03, 0xe8, 0xff, 0x38, 0x00, 0x00, 0x04, 0xb0, 0x00, 0x00, 0x00, 0xc8,
  0x03, 0xe8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x64, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0xff, 0xff, 0x04, 0xb0, 0x00, 0x00, 0x03, 0x20, 0x00, 0x00,
  0x03, 0x20, 0x00, 0x00, 0x03, 0x20, 0x00, 0x00, 0x03, 0x20, 0x00, 0x00,
  0x03, 0x20, 0x00, 0x00, 0x02, 0x58, 0x00, 0x00, 0x02, 0x58, 0x00, 0x00,
  0x02, 0x58, 0x00, 0x00, 0x03, 0x20, 0x00, 0x00, 0x01, 0x90, 0x00, 0x00,
  0x01, 0x90, 0x00, 0x00, 0x03, 0x20, 0x00, 0x00, 0x01, 0x90, 0x00, 0x00,
  0x04, 0xb0, 0x00, 0x00, 0x03, 0x20, 0x00, 0x00, 0x03, 0x20, 0x00, 0x00,
  0x03, 0x20, 0x00, 0x00, 0x03, 0x20, 0x00, 0x00, 0x02, 0x58, 0x00, 0x00,
  0x03, 0x20, 0x00, 0x00, 0x03, 0x20, 0x00, 0x00, 0x03, 0x20, 0x00, 0x00,
  0x03, 0x20, 0x00, 0x00, 0x04, 0xb0, 0x00, 0x00, 0x03, 0x20, 0x00, 0x00,
  0x03, 0x20, 0x00, 0x00, 0x03, 0x20, 0x00, 0x00, 0x03, 0x20, 0x00, 0x00,
  0x03, 0x20, 0x00, 0x00, 0x03, 0x20, 0x00, 0x00, 0x03, 0x20, 0x00, 0x00,
  0x03, 0x20, 0x00, 0x00, 0x03, 0x20, 0x00, 0x00, 0x03, 0x20, 0x00, 0x00,
  0x03, 0x20, 0x00, 0x00, 0x03, 0x20, 0x00, 0x00, 0x03, 0x20, 0x00, 0x00,
  0x03, 0xe8, 0x00, 0x00, 0x03, 0x20, 0x00, 0x00, 0x04, 0xb0, 0x00, 0x00,
  0x03, 0xe8, 0x00, 0x00, 0x03, 0xe8, 0x00, 0x00, 0x03, 0x20, 0x00, 0x00,
  0x03, 0xe8, 0x00, 0x00, 0x03, 0x20, 0x00, 0x00, 0x03, 0x20, 0x00, 0x00,
  0x03, 0x20, 0x00, 0x00, 0x03, 0xe8, 0x00, 0x00, 0x04, 0xb0, 0x00, 0x00,
  0x04, 0xb0, 0x00, 0x00, 0x03, 0x20, 0x00, 0x00, 0x03, 0x20, 0x00, 0x00,
  0x04, 0xb0, 0x00, 0x00, 0x03, 0xe8, 0x00, 0x00, 0x03, 0x20, 0x00, 0x00,
  0x03, 0x20, 0x00, 0x00, 0x03, 0x20, 0x00, 0x00, 0x03, 0x20, 0x00, 0x00,
  0x03, 0x20, 0x00, 0x00, 0x03, 0x20, 0x00, 0x00, 0x03, 0x20, 0x00, 0x00,
  0x03, 0x20, 0x00, 0x00, 0x03, 0x20, 0x00, 0x00, 0x03, 0x20, 0x00, 0x00,
  0x03, 0x20, 0x00, 0x00, 0x01, 0x90, 0x00, 0x00, 0x03, 0x20, 0x00, 0x00,
  0x04, 0xb0, 0x00, 0x00, 0x03, 0x20, 0x00, 0x00, 0x03, 0xe8, 0x00, 0x00,
  0x03, 0xe8, 0x00, 0x00, 0x01, 0x90, 0x00, 0x00, 0x02, 0x58, 0x00, 0x00,
  0x02, 0x58, 0x00, 0x00, 0x03, 0x20, 0x00, 0x00, 0x03, 0x20, 0x00, 0x00,
  0x01, 0x90, 0x00, 0x00, 0x03, 0x20, 0x00, 0x00, 0x01, 0x90, 0x00, 0x00,
  0x03, 0x20, 0x00, 0x00, 0x03, 0xe8, 0x00, 0x00, 0x01, 0x90, 0x00, 0x00,
  0x01, 0x90, 0x00, 0x00, 0x03, 0x20, 0x00, 0x00, 0x03, 0x20, 0x00, 0x00,
  0x03, 0x20, 0x00, 0x00, 0x03, 0x20, 0x00, 0x00, 0x02, 0x58, 0x00, 0x00,
  0x03, 0xe8, 0x00, 0x00, 0x02, 0x58, 0x00, 0x00, 0x03, 0x20, 0x00, 0x00,
  0x03, 0x20, 0x00, 0x00, 0x02, 0x58, 0x00, 0x00, 0x03, 0x20, 0x00, 0x00,
  0x01, 0x90, 0x00, 0x00, 0x03, 0x20, 0x00, 0x00, 0x03, 0xe8, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x27, 0x00, 0x3e,
  0x00, 0x57, 0x00, 0x6f, 0x00, 0x82, 0x00, 0x97, 0x00, 0x97, 0x00, 0xa8,
  0x00, 0xbe, 0x00, 0xd0, 0x00, 0xe2, 0x00, 0xfd, 0x01, 0x09, 0x01, 0x2a,
  0x01, 0x3f, 0x01, 0x5b, 0x01, 0x73, 0x01, 0x8b, 0x01, 0x99, 0x01, 0xa9,
  0x01, 0xbd, 0x01, 0xd4, 0x01, 0xee, 0x02, 0x12, 0x02, 0x34, 0x02, 0x4c,
  0x02, 0x5f, 0x02, 0x78, 0x02, 0x8c, 0x02, 0xa5, 0x02, 0xbb, 0x02, 0xcd,
  0x02, 0xdf, 0x02, 0xfa, 0x03, 0x0f, 0x03, 0x23, 0x03, 0x3a, 0x03, 0x59,
  0x03, 0x67, 0x03, 0x86, 0x03, 0xa0, 0x03, 0xbf, 0x03, 0xd7, 0x03, 0xf9,
  0x04, 0x16, 0x04, 0x31, 0x04, 0x42, 0x04, 0x5a, 0x04, 0x7c, 0x04, 0xa1,
  0x04, 0xc5, 0x04, 0xeb, 0x05, 0x0c, 0x05, 0x28, 0x05, 0x3b, 0x05, 0x56,
  0x05, 0x6f, 0x05, 0x83, 0x05, 0x97, 0x05, 0xb1, 0x05, 0xc6, 0x05, 0xe1,
  0x05, 0xfc, 0x06, 0x12, 0x06, 0x30, 0x06, 0x42, 0x06, 0x57, 0x06, 0x80,
  0x06, 0x9c, 0x06, 0xc5, 0x06, 0xe3, 0x06, 0xf0, 0x07, 0x07, 0x07, 0x1f,
  0x07, 0x3a, 0x07, 0x4d, 0x07, 0x59, 0x07, 0x65, 0x07, 0x70, 0x07, 0x89,
  0x07, 0xa7, 0x07, 0xb7, 0x07, 0xc8, 0x07, 0xe9, 0x08, 0x0c, 0x08, 0x1e,
  0x08, 0x35, 0x08, 0x46, 0x08, 0x64, 0x08, 0x75, 0x08, 0x8a, 0x08, 0x96,
  0x08, 0xa6, 0x08, 0xbc, 0x08, 0xc8, 0x08, 0xdd, 0x08, 0xfa, 0x00, 0x00,
  0x00, 0x01, 0x00, 0x00, 0x00, 0x64, 0x00, 0x20, 0x00, 0x06, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x10,
  0x00, 0xc6, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x09,
  0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x03,
  0x00, 0x09, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x07,
  0x00, 0x0c, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x03,
  0x00, 0x13, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x03,
  0x00, 0x16, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x05, 0x00, 0x0c,
  0x00, 0x19, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x00, 0x03,
  0x00, 0x25, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x00, 0x09,
  0x00, 0x28, 0x00, 0x03, 0x00, 0x01, 0x04, 0x09, 0x00, 0x00, 0x00, 0x6e,
  0x00, 0x31, 0x00, 0x03, 0x00, 0x01, 0x04, 0x09, 0x00, 0x01, 0x00, 0x08,
  0x00, 0x9f, 0x00, 0x03, 0x00, 0x01, 0x04, 0x09, 0x00, 0x02, 0x00, 0x0e,
  0x00, 0xa7, 0x00, 0x03, 0x00, 0x01, 0x04, 0x09, 0x00, 0x03, 0x00, 0x32,
  0x00, 0xb5, 0x00, 0x03, 0x00, 0x01, 0x04, 0x09, 0x00, 0x04, 0x00, 0x18,
  0x00, 0xe7, 0x00, 0x03, 0x00, 0x01, 0x04, 0x09, 0x00, 0x05, 0x00, 0x18,
  0x00, 0xff, 0x00, 0x03, 0x00, 0x01, 0x04, 0x09, 0x00, 0x06, 0x00, 0x16,
  0x01, 0x17, 0x00, 0x03, 0x00, 0x01, 0x04, 0x09, 0x00, 0x07, 0x00, 0x40,
  0x01, 0x2d, 0x43, 0x6f, 0x70, 0x79, 0x72, 0x69, 0x67, 0x68, 0x74, 0x4e,
  0x65, 0x77, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x72, 0x4e, 0x65, 0x77,
  0x4e, 0x65, 0x77, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x20, 0x31,
  0x2e, 0x30, 0x30, 0x4e, 0x65, 0x77, 0x54, 0x72, 0x61, 0x64, 0x65, 0x6d,
  0x61, 0x72, 0x6b, 0x00, 0x43, 0x00, 0x6f, 0x00, 0x70, 0x00, 0x79, 0x00,
  0x72, 0x00, 0x69, 0x00, 0x67, 0x00, 0x68, 0x00, 0x74, 0x00, 0x20, 0x00,
  0xa9, 0x00, 0x20, 0x00, 0x32, 0x00, 0x30, 0x00, 0x30, 0x00, 0x34, 0x00,
  0x20, 0x00, 0x62, 0x00, 0x79, 0x00, 0x20, 0x00, 0x4d, 0x00, 0x61, 0x00,
  0x74, 0x00, 0x74, 0x00, 0x68, 0x00, 0x65, 0x00, 0x77, 0x00, 0x20, 0x00,
  0x57, 0x00, 0x65, 0x00, 0x6c, 0x00, 0x63, 0x00, 0x68, 0x00, 0x2e, 0x00,
  0x20, 0x00, 0x41, 0x00, 0x6c, 0x00, 0x6c, 0x00, 0x20, 0x00, 0x52, 0x00,
  0x69, 0x00, 0x67, 0x00, 0x68, 0x00, 0x74, 0x00, 0x73, 0x00, 0x20, 0x00,
  0x52, 0x00, 0x65, 0x00, 0x73, 0x00, 0x65, 0x00, 0x72, 0x00, 0x76, 0x00,
  0x65, 0x00, 0x64, 0x00, 0x2e, 0x00, 0x54, 0x00, 0x69, 0x00, 0x6e, 0x00,
  0x79, 0x00, 0x52, 0x00, 0x65, 0x00, 0x67, 0x00, 0x75, 0x00, 0x6c, 0x00,
  0x61, 0x00, 0x72, 0x00, 0x54, 0x00, 0x69, 0x00, 0x6e, 0x00, 0x79, 0x00,
  0x20, 0x00, 0x52, 0x00, 0x65, 0x00, 0x67, 0x00, 0x75, 0x00, 0x6c, 0x00,
  0x61, 0x00, 0x72, 0x00, 0x3a, 0x00, 0x56, 0x00, 0x65, 0x00, 0x72, 0x00,
  0x73, 0x00, 0x69, 0x00, 0x6f, 0x00, 0x6e, 0x00, 0x20, 0x00, 0x31, 0x00,
  0x2e, 0x00, 0x30, 0x00, 0x30, 0x00, 0x54, 0x00, 0x69, 0x00, 0x6e, 0x00,
  0x79, 0x00, 0x20, 0x00, 0x52, 0x00, 0x65, 0x00, 0x67, 0x00, 0x75, 0x00,
  0x6c, 0x00, 0x61, 0x00, 0x72, 0x00, 0x56, 0x00, 0x65, 0x00, 0x72, 0x00,
  0x73, 0x00, 0x69, 0x00, 0x6f, 0x00, 0x6e, 0x00, 0x20, 0x00, 0x31, 0x00,
  0x2e, 0x00, 0x30, 0x00, 0x30, 0x00, 0x54, 0x00, 0x69, 0x00, 0x6e, 0x00,
  0x79, 0x00, 0x52, 0x00, 0x65, 0x00, 0x67, 0x00, 0x75, 0x00, 0x6c, 0x00,
  0x61, 0x00, 0x72, 0x00, 0x54, 0x00, 0x69, 0x00, 0x6e, 0x00, 0x79, 0x00,
  0x99, 0x00, 0x20, 0x00, 0x54, 0x00, 0x72, 0x00, 0x61, 0x00, 0x64, 0x00,
  0x65, 0x00, 0x6d, 0x00, 0x61, 0x00, 0x72, 0x00, 0x6b, 0x00, 0x20, 0x00,
  0x6f, 0x00, 0x66, 0x00, 0x20, 0x00, 0x4d, 0x00, 0x61, 0x00, 0x74, 0x00,
  0x74, 0x00, 0x68, 0x00, 0x65, 0x00, 0x77, 0x00, 0x20, 0x00, 0x57, 0x00,
  0x65, 0x00, 0x6c, 0x00, 0x63, 0x00, 0x68, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
};
unsigned int tiny_ttf_len = 6656;
