build/
buildbot/
/VERSION.txt

*.so
*.so.*
*.dll
*.exe
*.o
*.obj
*.res
*.lib
*.a
*.la
*.dSYM
*,e1f
*,ff8
*.lnk
*.err
*.exp
*.map
*.orig
*~
*.swp
*.tmp
*.rej

# for CMake
CMakeFiles/
CMakeCache.txt
cmake_install.cmake
cmake_uninstall.cmake
SDL3ConfigVersion.cmake
.ninja_*
*.ninja

# for CLion
.idea
cmake-build-*

# for Xcode
*.mode1*
*.perspective*
*.pbxuser
(^|/)build($|/)
.DS_Store
xcuserdata
*.xcworkspace

# for Visual C++
.vs
Debug
Release
*.user
*.ncb
*.suo
*.sdf

# for Clangd
.cache
