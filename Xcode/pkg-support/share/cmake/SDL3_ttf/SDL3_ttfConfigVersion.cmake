# based on the files generated by CMake's write_basic_package_version_file

# SDL CMake version configuration file:
# This file is meant to be placed in share/cmake/SDL3_ttf, next to SDL3_ttf.xcframework

cmake_minimum_required(VERSION 3.12...3.28)

get_filename_component(_sdl3_ttf_xcframework_parent_path "${CMAKE_CURRENT_LIST_DIR}" REALPATH)                  # /share/cmake/SDL3_ttf/
get_filename_component(_sdl3_ttf_xcframework_parent_path "${_sdl3_ttf_xcframework_parent_path}" REALPATH)       # /share/cmake/SDL3_ttf/
get_filename_component(_sdl3_ttf_xcframework_parent_path "${_sdl3_ttf_xcframework_parent_path}" PATH)           # /share/cmake
get_filename_component(_sdl3_ttf_xcframework_parent_path "${_sdl3_ttf_xcframework_parent_path}" PATH)           # /share
get_filename_component(_sdl3_ttf_xcframework_parent_path "${_sdl3_ttf_xcframework_parent_path}" PATH)           # /
set(_sdl3_ttf_xcframework "${_sdl3_ttf_xcframework_parent_path}/SDL3_ttf.xcframework")                          # /SDL3_ttf.xcframework
set(_sdl3_ttf_framework "${_sdl3_ttf_xcframework}/macos-arm64_x86_64/SDL3_ttf.framework")                       # /SDL3_ttf.xcframework/macos-arm64_x86_64/SDL3_ttf.framework
set(_sdl3_ttf_version_h "${_sdl3_ttf_framework}/Headers/SDL_ttf.h")                                             # /SDL3_ttf.xcframework/macos-arm64_x86_64/SDL3_ttf.framework/Headers/SDL_ttf.h

if(NOT EXISTS "${_sdl3_ttf_version_h}")
    message(AUTHOR_WARNING "Cannot not find ${_sdl3_ttf_framework}. This script is meant to be placed in share/cmake/SDL3, next to SDL3.xcframework")
    return()
endif()

file(READ "${_sdl3_ttf_version_h}" _sdl_version_h)

unset(_sdl3_ttf_xcframework_parent_path)
unset(_sdl3_ttf_framework)
unset(_sdl3_ttf_xcframework)
unset(_sdl3_ttf_version_h)

string(REGEX MATCH "#define[ \t]+SDL_TTF_MAJOR_VERSION[ \t]+([0-9]+)" _sdl_major_re "${_sdl_version_h}")
set(_sdl_major "${CMAKE_MATCH_1}")
string(REGEX MATCH "#define[ \t]+SDL_TTF_MINOR_VERSION[ \t]+([0-9]+)" _sdl_minor_re "${_sdl_version_h}")
set(_sdl_minor "${CMAKE_MATCH_1}")
string(REGEX MATCH "#define[ \t]+SDL_TTF_MICRO_VERSION[ \t]+([0-9]+)" _sdl_micro_re "${_sdl_version_h}")
set(_sdl_micro "${CMAKE_MATCH_1}")
if(_sdl_major_re AND _sdl_minor_re AND _sdl_micro_re)
    set(PACKAGE_VERSION "${_sdl_major}.${_sdl_minor}.${_sdl_micro}")
else()
    message(AUTHOR_WARNING "Could not extract version from SDL_ttf.h.")
    return()
endif()

unset(_sdl_major_re)
unset(_sdl_major)
unset(_sdl_minor_re)
unset(_sdl_minor)
unset(_sdl_micro_re)
unset(_sdl_micro)

if(PACKAGE_FIND_VERSION_RANGE)
    # Package version must be in the requested version range
    if ((PACKAGE_FIND_VERSION_RANGE_MIN STREQUAL "INCLUDE" AND PACKAGE_VERSION VERSION_LESS PACKAGE_FIND_VERSION_MIN)
        OR ((PACKAGE_FIND_VERSION_RANGE_MAX STREQUAL "INCLUDE" AND PACKAGE_VERSION VERSION_GREATER PACKAGE_FIND_VERSION_MAX)
        OR (PACKAGE_FIND_VERSION_RANGE_MAX STREQUAL "EXCLUDE" AND PACKAGE_VERSION VERSION_GREATER_EQUAL PACKAGE_FIND_VERSION_MAX)))
        set(PACKAGE_VERSION_COMPATIBLE FALSE)
    else()
        set(PACKAGE_VERSION_COMPATIBLE TRUE)
    endif()
else()
    if(PACKAGE_VERSION VERSION_LESS PACKAGE_FIND_VERSION)
        set(PACKAGE_VERSION_COMPATIBLE FALSE)
    else()
        set(PACKAGE_VERSION_COMPATIBLE TRUE)
        if(PACKAGE_FIND_VERSION STREQUAL PACKAGE_VERSION)
            set(PACKAGE_VERSION_EXACT TRUE)
        endif()
    endif()
endif()

# The SDL3_ttf.xcframework only contains 64-bit archives
if(NOT "${CMAKE_SIZEOF_VOID_P}" EQUAL "8")
    set(PACKAGE_VERSION_UNSUITABLE TRUE)
endif()

if(NOT CMAKE_SYSTEM_NAME MATCHES "^(Darwin|iOS|tvOS)$")
    set(PACKAGE_VERSION_UNSUITABLE TRUE)
endif()
