
# Using this package

This package contains SDL_ttf built for Xcode, and includes support for macOS, iOS and tvOS.

To use this package in Xcode, drag `SDL3_ttf.xcframework` into your project.

To use this package in a CMake project, copy both `SDL3_ttf.xcframework` and `share` to `~/Library/Frameworks`.

# Documentation

An API reference and additional documentation is available at:

https://wiki.libsdl.org/SDL3_ttf

# Discussions

## Discord

You can join the official Discord server at:

https://discord.com/invite/BwpFGBWsv8

## Forums/mailing lists

You can join SDL development discussions at:

https://discourse.libsdl.org/

Once you sign up, you can use the forum through the website or as a mailing list from your email client.

## Announcement list

You can sign up for the low traffic announcement list at:

https://www.libsdl.org/mailing-list.php

