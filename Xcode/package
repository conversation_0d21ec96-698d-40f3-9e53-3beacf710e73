#! /bin/csh -ef

set prog = `/usr/bin/basename $0`
set usage = "Usage: $prog [-f] root-dir info-file [tiff-file] [-d dest-dir] [-r resource-dir] [-traditional | -gnutar]"
set noglob

if (-x /usr/bin/mkbom) then
    set mkbom=/usr/bin/mkbom
    set lsbom=/usr/bin/lsbom
else
    set mkbom=/usr/etc/mkbom
    set lsbom=/usr/etc/lsbom
endif

if (-x /usr/bin/awk) then
    set awk=/usr/bin/awk
else
    set awk=/bin/awk
endif

set gnutar=/usr/bin/gnutar
set tar=/usr/bin/tar
set pax=/bin/pax

# gather parameters
if ($#argv == 0) then
    echo $usage
    exit(1)
endif

while ( $#argv > 0 )
    switch ( $argv[1] )
	case -d:
	    if ( $?destDir ) then
		echo ${prog}: dest-dir parameter already set to ${destDir}.
		echo $usage
		exit(1)
	    else if ( $#argv < 2 ) then
		echo ${prog}: -d option requires destination directory.
		echo $usage
		exit(1)
	    else
		set destDir = $argv[2]
		shift; shift
		breaksw
	    endif
	case -f:
	    if ( $?rootDir ) then
		echo ${prog}: root-dir parameter already set to ${rootDir}.
		echo $usage
		exit(1)
	    else if ( $#argv < 2 ) then
		echo ${prog}: -f option requires package root directory.
		echo $usage
		exit(1)
	    else
		set rootDir = $argv[2]
		set fflag
		shift; shift
		breaksw
	    endif
	case -r:
	    if ( $?resDir ) then
		echo ${prog}: resource-dir parameter already set to ${resDir}.
		echo $usage
		exit(1)
	    else if ( $#argv < 2 ) then
		echo ${prog}: -r option requires package resource directory.
		echo $usage
		exit(1)
	    else
		set resDir = $argv[2]
		shift; shift
		breaksw
	    endif
	case -traditional:
	    set usetar
            unset usegnutar
	    unset usepax
	    breaksw
        case -gnutar:
	    set usegnutar
            unset usepax
	    unset usetar
	case -B:
	    # We got long file names, better use bigtar instead
	    #set archiver = /NextAdmin/Installer.app/Resources/installer_bigtar
	    echo 2>&1 ${prog}: -B flag is no longer relevant.
	    shift
	    breaksw
	case -*:
	    echo ${prog}: Unknown option: $argv[1]
	    echo $usage
	    exit(1)
	case *.info:
	    if ( $?info ) then
		echo ${prog}: info-file parameter already set to ${info}.
		echo $usage
		exit(1)
	    else
		set info = "$argv[1]"
		shift
		breaksw
	    endif
	case *.tiff:
	    if ( $?tiff ) then
		echo ${prog}: tiff-file parameter already set to ${tiff}.
		echo $usage
		exit(1)
	    else
		set tiff = "$argv[1]"
		shift
		breaksw
	    endif
	default:
	    if ( $?rootDir ) then
		echo ${prog}: unrecognized parameter: $argv[1]
		echo $usage
		exit(1)
	    else
		set rootDir = "$argv[1]"
		shift
		breaksw
	    endif
    endsw
end

# check for mandatory parameters
if ( ! $?rootDir ) then
    echo ${prog}: missing root-dir parameter.
    echo $usage
    exit(1)
else if ( ! $?info) then
    echo ${prog}: missing info-file parameter.
    echo $usage
    exit(1)
endif

# destDir gets default value if unset on command line
if ( $?destDir ) then
    /bin/mkdir -p $destDir
else
    set destDir = .
endif

# derive the root name for the package from the root name of the info file
set root = `/usr/bin/basename $info .info`

# create package directory
set pkg = ${destDir}/${root}.pkg
echo Generating Installer package $pkg ...
if ( -e $pkg ) /bin/rm -rf $pkg
/bin/mkdir -p -m 755 $pkg

# (gnu)tar/pax and compress root directory to package archive
echo -n "	creating package archive ... "
if ( $?fflag ) then
    set pkgTop = ${rootDir:t}
    set parent = ${rootDir:h}
    if ( "$parent" == "$pkgTop" ) set parent = "."
else
    set parent = $rootDir
    set pkgTop = .    
endif
if ( $?usetar ) then
    set pkgArchive = $pkg/$root.tar.Z
    (cd $parent; $tar -w $pkgTop) | /usr/bin/compress -f -c > $pkgArchive
else if ( $?usegnutar ) then
    set pkgArchive = $pkg/$root.tar.gz
    (cd $parent; $gnutar zcf $pkgArchive $pkgTop)
else
    set pkgArchive = $pkg/$root.pax.gz
    (cd $parent; $pax -w -z -x cpio $pkgTop) > $pkgArchive
endif
/bin/chmod 444 $pkgArchive
echo done.

# copy info file to package
set pkgInfo = $pkg/$root.info
echo -n "	copying ${info:t} ... "
/bin/cp $info $pkgInfo
/bin/chmod 444 $pkgInfo
echo done.

# copy tiff file to package
if ( $?tiff ) then
    set pkgTiff = $pkg/$root.tiff
    echo -n "	copying ${tiff:t} ... "
    /bin/cp $tiff $pkgTiff
    /bin/chmod 444 $pkgTiff
    echo done.
endif

# copy resources to package
if ( $?resDir ) then
    echo -n "	copying ${resDir:t} ... "

    # don't want to see push/pop output
    pushd $resDir > /dev/null
	# get lists of resources. We'll want to change
	# permissions on just these things later.
        set directoriesInResDir = `find . -type d`
        set filesInResDir = `find . -type f`
    popd > /dev/null

    # copy the resource directory contents into the package directory
    foreach resFile (`ls $resDir`)
	cp -r $resDir/$resFile $pkg
    end

    pushd $pkg > /dev/null
	# Change all directories to +r+x, except the package
	# directory itself
        foreach resFileItem ($directoriesInResDir)
            if ( $resFileItem != "." ) then
                chmod 555 $resFileItem
            endif
        end
	# change all flat files to read only
        foreach resFileItem ($filesInResDir)
            chmod 444 $resFileItem
        end
    popd > /dev/null

    echo done.
endif

# generate bom file
set pkgBom = $pkg/$root.bom
echo -n "	generating bom file ... "
/bin/rm -f $pkgBom
if ( $?fflag ) then
    $mkbom $parent $pkgBom >& /dev/null
else
    $mkbom $rootDir $pkgBom >& /dev/null
endif
/bin/chmod 444 $pkgArchive
echo done.
	
# generate sizes file
set pkgSizes = $pkg/$root.sizes
echo -n "	generating sizes file ... "

# compute number of files in package
set numFiles = `$lsbom -s $pkgBom | /usr/bin/wc -l`

# compute package size when compressed
@ compressedSize = `/usr/bin/du -k -s $pkg | $awk '{print $1}'`
@ compressedSize += 3		# add 1KB each for sizes, location, status files

@ infoSize = `/bin/ls -s $pkgInfo | $awk '{print $1}'`
@ bomSize = `/bin/ls -s $pkgBom | $awk '{print $1}'`
if ( $?tiff ) then
    @ tiffSize = `/bin/ls -s $pkgTiff | $awk '{print $1}'`
else
    @ tiffSize = 0
endif 

@ installedSize = `/usr/bin/du -k -s $rootDir | $awk '{print $1}'`
@ installedSize += $infoSize + $bomSize + $tiffSize + 3

# echo size parameters to sizes file
echo NumFiles $numFiles             >  $pkgSizes
echo InstalledSize $installedSize   >> $pkgSizes
echo CompressedSize $compressedSize >> $pkgSizes
echo done.
echo " ... finished generating $pkg."

exit(0)

# end package
	
