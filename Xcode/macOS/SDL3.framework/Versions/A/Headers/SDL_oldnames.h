/*
  Simple DirectMedia Layer
  Copyright (C) 1997-2025 <PERSON> <<EMAIL>>

  This software is provided 'as-is', without any express or implied
  warranty.  In no event will the authors be held liable for any damages
  arising from the use of this software.

  Permission is granted to anyone to use this software for any purpose,
  including commercial applications, and to alter it and redistribute it
  freely, subject to the following restrictions:

  1. The origin of this software must not be misrepresented; you must not
     claim that you wrote the original software. If you use this software
     in a product, an acknowledgment in the product documentation would be
     appreciated but is not required.
  2. Altered source versions must be plainly marked as such, and must not be
     misrepresented as being the original software.
  3. This notice may not be removed or altered from any source distribution.
*/

/*
 * Definitions to ease transition from SDL2 code
 */

#ifndef SDL_oldnames_h_
#define SDL_oldnames_h_

#include <SDL3/SDL_platform_defines.h>

/* The new function names are recommended, but if you want to have the
 * old names available while you are in the process of migrating code
 * to SDL3, you can define `SDL_ENABLE_OLD_NAMES` in your project.
 *
 * You can use https://github.com/libsdl-org/SDL/blob/main/build-scripts/rename_symbols.py to mass rename the symbols defined here in your codebase:
 *  rename_symbols.py --all-symbols source_code_path
 */
#ifdef SDL_ENABLE_OLD_NAMES

/* ##SDL_atomic.h */
#define SDL_AtomicAdd SDL_AddAtomicInt
#define SDL_AtomicCAS SDL_CompareAndSwapAtomicInt
#define SDL_AtomicCASPtr SDL_CompareAndSwapAtomicPointer
#define SDL_AtomicGet SDL_GetAtomicInt
#define SDL_AtomicGetPtr SDL_GetAtomicPointer
#define SDL_AtomicLock SDL_LockSpinlock
#define SDL_AtomicSet SDL_SetAtomicInt
#define SDL_AtomicSetPtr SDL_SetAtomicPointer
#define SDL_AtomicTryLock SDL_TryLockSpinlock
#define SDL_AtomicUnlock SDL_UnlockSpinlock
#define SDL_atomic_t SDL_AtomicInt

/* ##SDL_audio.h */
#define AUDIO_F32 SDL_AUDIO_F32LE
#define AUDIO_F32LSB SDL_AUDIO_F32LE
#define AUDIO_F32MSB SDL_AUDIO_F32BE
#define AUDIO_F32SYS SDL_AUDIO_F32
#define AUDIO_S16 SDL_AUDIO_S16LE
#define AUDIO_S16LSB SDL_AUDIO_S16LE
#define AUDIO_S16MSB SDL_AUDIO_S16BE
#define AUDIO_S16SYS SDL_AUDIO_S16
#define AUDIO_S32 SDL_AUDIO_S32LE
#define AUDIO_S32LSB SDL_AUDIO_S32LE
#define AUDIO_S32MSB SDL_AUDIO_S32BE
#define AUDIO_S32SYS SDL_AUDIO_S32
#define AUDIO_S8 SDL_AUDIO_S8
#define AUDIO_U8 SDL_AUDIO_U8
#define SDL_AudioStreamAvailable SDL_GetAudioStreamAvailable
#define SDL_AudioStreamClear SDL_ClearAudioStream
#define SDL_AudioStreamFlush SDL_FlushAudioStream
#define SDL_AudioStreamGet SDL_GetAudioStreamData
#define SDL_AudioStreamPut SDL_PutAudioStreamData
#define SDL_FreeAudioStream SDL_DestroyAudioStream
#define SDL_FreeWAV SDL_free
#define SDL_LoadWAV_RW SDL_LoadWAV_IO
#define SDL_MixAudioFormat SDL_MixAudio
#define SDL_NewAudioStream SDL_CreateAudioStream

/* ##SDL_cpuinfo.h */
#define SDL_GetCPUCount SDL_GetNumLogicalCPUCores
#define SDL_SIMDGetAlignment SDL_GetSIMDAlignment

/* ##SDL_endian.h */
#define SDL_SwapBE16 SDL_Swap16BE
#define SDL_SwapBE32 SDL_Swap32BE
#define SDL_SwapBE64 SDL_Swap64BE
#define SDL_SwapLE16 SDL_Swap16LE
#define SDL_SwapLE32 SDL_Swap32LE
#define SDL_SwapLE64 SDL_Swap64LE

/* ##SDL_events.h */
#define SDL_APP_DIDENTERBACKGROUND SDL_EVENT_DID_ENTER_BACKGROUND
#define SDL_APP_DIDENTERFOREGROUND SDL_EVENT_DID_ENTER_FOREGROUND
#define SDL_APP_LOWMEMORY SDL_EVENT_LOW_MEMORY
#define SDL_APP_TERMINATING SDL_EVENT_TERMINATING
#define SDL_APP_WILLENTERBACKGROUND SDL_EVENT_WILL_ENTER_BACKGROUND
#define SDL_APP_WILLENTERFOREGROUND SDL_EVENT_WILL_ENTER_FOREGROUND
#define SDL_AUDIODEVICEADDED SDL_EVENT_AUDIO_DEVICE_ADDED
#define SDL_AUDIODEVICEREMOVED SDL_EVENT_AUDIO_DEVICE_REMOVED
#define SDL_CLIPBOARDUPDATE SDL_EVENT_CLIPBOARD_UPDATE
#define SDL_CONTROLLERAXISMOTION SDL_EVENT_GAMEPAD_AXIS_MOTION
#define SDL_CONTROLLERBUTTONDOWN SDL_EVENT_GAMEPAD_BUTTON_DOWN
#define SDL_CONTROLLERBUTTONUP SDL_EVENT_GAMEPAD_BUTTON_UP
#define SDL_CONTROLLERDEVICEADDED SDL_EVENT_GAMEPAD_ADDED
#define SDL_CONTROLLERDEVICEREMAPPED SDL_EVENT_GAMEPAD_REMAPPED
#define SDL_CONTROLLERDEVICEREMOVED SDL_EVENT_GAMEPAD_REMOVED
#define SDL_CONTROLLERSENSORUPDATE SDL_EVENT_GAMEPAD_SENSOR_UPDATE
#define SDL_CONTROLLERSTEAMHANDLEUPDATED SDL_EVENT_GAMEPAD_STEAM_HANDLE_UPDATED
#define SDL_CONTROLLERTOUCHPADDOWN SDL_EVENT_GAMEPAD_TOUCHPAD_DOWN
#define SDL_CONTROLLERTOUCHPADMOTION SDL_EVENT_GAMEPAD_TOUCHPAD_MOTION
#define SDL_CONTROLLERTOUCHPADUP SDL_EVENT_GAMEPAD_TOUCHPAD_UP
#define SDL_ControllerAxisEvent SDL_GamepadAxisEvent
#define SDL_ControllerButtonEvent SDL_GamepadButtonEvent
#define SDL_ControllerDeviceEvent SDL_GamepadDeviceEvent
#define SDL_ControllerSensorEvent SDL_GamepadSensorEvent
#define SDL_ControllerTouchpadEvent SDL_GamepadTouchpadEvent
#define SDL_DISPLAYEVENT_CONNECTED SDL_EVENT_DISPLAY_ADDED
#define SDL_DISPLAYEVENT_DISCONNECTED SDL_EVENT_DISPLAY_REMOVED
#define SDL_DISPLAYEVENT_MOVED SDL_EVENT_DISPLAY_MOVED
#define SDL_DISPLAYEVENT_ORIENTATION SDL_EVENT_DISPLAY_ORIENTATION
#define SDL_DROPBEGIN SDL_EVENT_DROP_BEGIN
#define SDL_DROPCOMPLETE SDL_EVENT_DROP_COMPLETE
#define SDL_DROPFILE SDL_EVENT_DROP_FILE
#define SDL_DROPTEXT SDL_EVENT_DROP_TEXT
#define SDL_DelEventWatch SDL_RemoveEventWatch
#define SDL_FINGERDOWN SDL_EVENT_FINGER_DOWN
#define SDL_FINGERMOTION SDL_EVENT_FINGER_MOTION
#define SDL_FINGERUP SDL_EVENT_FINGER_UP
#define SDL_FIRSTEVENT SDL_EVENT_FIRST
#define SDL_JOYAXISMOTION SDL_EVENT_JOYSTICK_AXIS_MOTION
#define SDL_JOYBATTERYUPDATED SDL_EVENT_JOYSTICK_BATTERY_UPDATED
#define SDL_JOYBUTTONDOWN SDL_EVENT_JOYSTICK_BUTTON_DOWN
#define SDL_JOYBUTTONUP SDL_EVENT_JOYSTICK_BUTTON_UP
#define SDL_JOYDEVICEADDED SDL_EVENT_JOYSTICK_ADDED
#define SDL_JOYDEVICEREMOVED SDL_EVENT_JOYSTICK_REMOVED
#define SDL_JOYBALLMOTION SDL_EVENT_JOYSTICK_BALL_MOTION
#define SDL_JOYHATMOTION SDL_EVENT_JOYSTICK_HAT_MOTION
#define SDL_KEYDOWN SDL_EVENT_KEY_DOWN
#define SDL_KEYMAPCHANGED SDL_EVENT_KEYMAP_CHANGED
#define SDL_KEYUP SDL_EVENT_KEY_UP
#define SDL_LASTEVENT SDL_EVENT_LAST
#define SDL_LOCALECHANGED SDL_EVENT_LOCALE_CHANGED
#define SDL_MOUSEBUTTONDOWN SDL_EVENT_MOUSE_BUTTON_DOWN
#define SDL_MOUSEBUTTONUP SDL_EVENT_MOUSE_BUTTON_UP
#define SDL_MOUSEMOTION SDL_EVENT_MOUSE_MOTION
#define SDL_MOUSEWHEEL SDL_EVENT_MOUSE_WHEEL
#define SDL_POLLSENTINEL SDL_EVENT_POLL_SENTINEL
#define SDL_QUIT SDL_EVENT_QUIT
#define SDL_RENDER_DEVICE_RESET SDL_EVENT_RENDER_DEVICE_RESET
#define SDL_RENDER_TARGETS_RESET SDL_EVENT_RENDER_TARGETS_RESET
#define SDL_SENSORUPDATE SDL_EVENT_SENSOR_UPDATE
#define SDL_TEXTEDITING SDL_EVENT_TEXT_EDITING
#define SDL_TEXTEDITING_EXT SDL_EVENT_TEXT_EDITING_EXT
#define SDL_TEXTINPUT SDL_EVENT_TEXT_INPUT
#define SDL_USEREVENT SDL_EVENT_USER
#define SDL_WINDOWEVENT_CLOSE SDL_EVENT_WINDOW_CLOSE_REQUESTED
#define SDL_WINDOWEVENT_DISPLAY_CHANGED SDL_EVENT_WINDOW_DISPLAY_CHANGED
#define SDL_WINDOWEVENT_ENTER SDL_EVENT_WINDOW_MOUSE_ENTER
#define SDL_WINDOWEVENT_EXPOSED SDL_EVENT_WINDOW_EXPOSED
#define SDL_WINDOWEVENT_FOCUS_GAINED SDL_EVENT_WINDOW_FOCUS_GAINED
#define SDL_WINDOWEVENT_FOCUS_LOST SDL_EVENT_WINDOW_FOCUS_LOST
#define SDL_WINDOWEVENT_HIDDEN SDL_EVENT_WINDOW_HIDDEN
#define SDL_WINDOWEVENT_HIT_TEST SDL_EVENT_WINDOW_HIT_TEST
#define SDL_WINDOWEVENT_ICCPROF_CHANGED SDL_EVENT_WINDOW_ICCPROF_CHANGED
#define SDL_WINDOWEVENT_LEAVE SDL_EVENT_WINDOW_MOUSE_LEAVE
#define SDL_WINDOWEVENT_MAXIMIZED SDL_EVENT_WINDOW_MAXIMIZED
#define SDL_WINDOWEVENT_MINIMIZED SDL_EVENT_WINDOW_MINIMIZED
#define SDL_WINDOWEVENT_MOVED SDL_EVENT_WINDOW_MOVED
#define SDL_WINDOWEVENT_RESIZED SDL_EVENT_WINDOW_RESIZED
#define SDL_WINDOWEVENT_RESTORED SDL_EVENT_WINDOW_RESTORED
#define SDL_WINDOWEVENT_SHOWN SDL_EVENT_WINDOW_SHOWN
#define SDL_WINDOWEVENT_SIZE_CHANGED SDL_EVENT_WINDOW_PIXEL_SIZE_CHANGED
#define SDL_eventaction SDL_EventAction

/* ##SDL_gamecontroller.h */
#define SDL_CONTROLLER_AXIS_INVALID SDL_GAMEPAD_AXIS_INVALID
#define SDL_CONTROLLER_AXIS_LEFTX SDL_GAMEPAD_AXIS_LEFTX
#define SDL_CONTROLLER_AXIS_LEFTY SDL_GAMEPAD_AXIS_LEFTY
#define SDL_CONTROLLER_AXIS_MAX SDL_GAMEPAD_AXIS_COUNT
#define SDL_CONTROLLER_AXIS_RIGHTX SDL_GAMEPAD_AXIS_RIGHTX
#define SDL_CONTROLLER_AXIS_RIGHTY SDL_GAMEPAD_AXIS_RIGHTY
#define SDL_CONTROLLER_AXIS_TRIGGERLEFT SDL_GAMEPAD_AXIS_LEFT_TRIGGER
#define SDL_CONTROLLER_AXIS_TRIGGERRIGHT SDL_GAMEPAD_AXIS_RIGHT_TRIGGER
#define SDL_CONTROLLER_BINDTYPE_AXIS SDL_GAMEPAD_BINDTYPE_AXIS
#define SDL_CONTROLLER_BINDTYPE_BUTTON SDL_GAMEPAD_BINDTYPE_BUTTON
#define SDL_CONTROLLER_BINDTYPE_HAT SDL_GAMEPAD_BINDTYPE_HAT
#define SDL_CONTROLLER_BINDTYPE_NONE SDL_GAMEPAD_BINDTYPE_NONE
#define SDL_CONTROLLER_BUTTON_A SDL_GAMEPAD_BUTTON_SOUTH
#define SDL_CONTROLLER_BUTTON_B SDL_GAMEPAD_BUTTON_EAST
#define SDL_CONTROLLER_BUTTON_BACK SDL_GAMEPAD_BUTTON_BACK
#define SDL_CONTROLLER_BUTTON_DPAD_DOWN SDL_GAMEPAD_BUTTON_DPAD_DOWN
#define SDL_CONTROLLER_BUTTON_DPAD_LEFT SDL_GAMEPAD_BUTTON_DPAD_LEFT
#define SDL_CONTROLLER_BUTTON_DPAD_RIGHT SDL_GAMEPAD_BUTTON_DPAD_RIGHT
#define SDL_CONTROLLER_BUTTON_DPAD_UP SDL_GAMEPAD_BUTTON_DPAD_UP
#define SDL_CONTROLLER_BUTTON_GUIDE SDL_GAMEPAD_BUTTON_GUIDE
#define SDL_CONTROLLER_BUTTON_INVALID SDL_GAMEPAD_BUTTON_INVALID
#define SDL_CONTROLLER_BUTTON_LEFTSHOULDER SDL_GAMEPAD_BUTTON_LEFT_SHOULDER
#define SDL_CONTROLLER_BUTTON_LEFTSTICK SDL_GAMEPAD_BUTTON_LEFT_STICK
#define SDL_CONTROLLER_BUTTON_MAX SDL_GAMEPAD_BUTTON_COUNT
#define SDL_CONTROLLER_BUTTON_MISC1 SDL_GAMEPAD_BUTTON_MISC1
#define SDL_CONTROLLER_BUTTON_PADDLE1 SDL_GAMEPAD_BUTTON_RIGHT_PADDLE1
#define SDL_CONTROLLER_BUTTON_PADDLE2 SDL_GAMEPAD_BUTTON_LEFT_PADDLE1
#define SDL_CONTROLLER_BUTTON_PADDLE3 SDL_GAMEPAD_BUTTON_RIGHT_PADDLE2
#define SDL_CONTROLLER_BUTTON_PADDLE4 SDL_GAMEPAD_BUTTON_LEFT_PADDLE2
#define SDL_CONTROLLER_BUTTON_RIGHTSHOULDER SDL_GAMEPAD_BUTTON_RIGHT_SHOULDER
#define SDL_CONTROLLER_BUTTON_RIGHTSTICK SDL_GAMEPAD_BUTTON_RIGHT_STICK
#define SDL_CONTROLLER_BUTTON_START SDL_GAMEPAD_BUTTON_START
#define SDL_CONTROLLER_BUTTON_TOUCHPAD SDL_GAMEPAD_BUTTON_TOUCHPAD
#define SDL_CONTROLLER_BUTTON_X SDL_GAMEPAD_BUTTON_WEST
#define SDL_CONTROLLER_BUTTON_Y SDL_GAMEPAD_BUTTON_NORTH
#define SDL_CONTROLLER_TYPE_NINTENDO_SWITCH_JOYCON_LEFT SDL_GAMEPAD_TYPE_NINTENDO_SWITCH_JOYCON_LEFT
#define SDL_CONTROLLER_TYPE_NINTENDO_SWITCH_JOYCON_PAIR SDL_GAMEPAD_TYPE_NINTENDO_SWITCH_JOYCON_PAIR
#define SDL_CONTROLLER_TYPE_NINTENDO_SWITCH_JOYCON_RIGHT SDL_GAMEPAD_TYPE_NINTENDO_SWITCH_JOYCON_RIGHT
#define SDL_CONTROLLER_TYPE_NINTENDO_SWITCH_PRO SDL_GAMEPAD_TYPE_NINTENDO_SWITCH_PRO
#define SDL_CONTROLLER_TYPE_PS3 SDL_GAMEPAD_TYPE_PS3
#define SDL_CONTROLLER_TYPE_PS4 SDL_GAMEPAD_TYPE_PS4
#define SDL_CONTROLLER_TYPE_PS5 SDL_GAMEPAD_TYPE_PS5
#define SDL_CONTROLLER_TYPE_UNKNOWN SDL_GAMEPAD_TYPE_STANDARD
#define SDL_CONTROLLER_TYPE_VIRTUAL SDL_GAMEPAD_TYPE_VIRTUAL
#define SDL_CONTROLLER_TYPE_XBOX360 SDL_GAMEPAD_TYPE_XBOX360
#define SDL_CONTROLLER_TYPE_XBOXONE SDL_GAMEPAD_TYPE_XBOXONE
#define SDL_GameController SDL_Gamepad
#define SDL_GameControllerAddMapping SDL_AddGamepadMapping
#define SDL_GameControllerAddMappingsFromFile SDL_AddGamepadMappingsFromFile
#define SDL_GameControllerAddMappingsFromRW SDL_AddGamepadMappingsFromIO
#define SDL_GameControllerAxis SDL_GamepadAxis
#define SDL_GameControllerBindType SDL_GamepadBindingType
#define SDL_GameControllerButton SDL_GamepadButton
#define SDL_GameControllerClose SDL_CloseGamepad
#define SDL_GameControllerFromInstanceID SDL_GetGamepadFromID
#define SDL_GameControllerFromPlayerIndex SDL_GetGamepadFromPlayerIndex
#define SDL_GameControllerGetAppleSFSymbolsNameForAxis SDL_GetGamepadAppleSFSymbolsNameForAxis
#define SDL_GameControllerGetAppleSFSymbolsNameForButton SDL_GetGamepadAppleSFSymbolsNameForButton
#define SDL_GameControllerGetAttached SDL_GamepadConnected
#define SDL_GameControllerGetAxis SDL_GetGamepadAxis
#define SDL_GameControllerGetAxisFromString SDL_GetGamepadAxisFromString
#define SDL_GameControllerGetButton SDL_GetGamepadButton
#define SDL_GameControllerGetButtonFromString SDL_GetGamepadButtonFromString
#define SDL_GameControllerGetFirmwareVersion SDL_GetGamepadFirmwareVersion
#define SDL_GameControllerGetJoystick SDL_GetGamepadJoystick
#define SDL_GameControllerGetNumTouchpadFingers SDL_GetNumGamepadTouchpadFingers
#define SDL_GameControllerGetNumTouchpads SDL_GetNumGamepadTouchpads
#define SDL_GameControllerGetPlayerIndex SDL_GetGamepadPlayerIndex
#define SDL_GameControllerGetProduct SDL_GetGamepadProduct
#define SDL_GameControllerGetProductVersion SDL_GetGamepadProductVersion
#define SDL_GameControllerGetSensorData SDL_GetGamepadSensorData
#define SDL_GameControllerGetSensorDataRate SDL_GetGamepadSensorDataRate
#define SDL_GameControllerGetSerial SDL_GetGamepadSerial
#define SDL_GameControllerGetSteamHandle SDL_GetGamepadSteamHandle
#define SDL_GameControllerGetStringForAxis SDL_GetGamepadStringForAxis
#define SDL_GameControllerGetStringForButton SDL_GetGamepadStringForButton
#define SDL_GameControllerGetTouchpadFinger SDL_GetGamepadTouchpadFinger
#define SDL_GameControllerGetType SDL_GetGamepadType
#define SDL_GameControllerGetVendor SDL_GetGamepadVendor
#define SDL_GameControllerHasAxis SDL_GamepadHasAxis
#define SDL_GameControllerHasButton SDL_GamepadHasButton
#define SDL_GameControllerHasSensor SDL_GamepadHasSensor
#define SDL_GameControllerIsSensorEnabled SDL_GamepadSensorEnabled
#define SDL_GameControllerMapping SDL_GetGamepadMapping
#define SDL_GameControllerMappingForGUID SDL_GetGamepadMappingForGUID
#define SDL_GameControllerName SDL_GetGamepadName
#define SDL_GameControllerOpen SDL_OpenGamepad
#define SDL_GameControllerPath SDL_GetGamepadPath
#define SDL_GameControllerRumble SDL_RumbleGamepad
#define SDL_GameControllerRumbleTriggers SDL_RumbleGamepadTriggers
#define SDL_GameControllerSendEffect SDL_SendGamepadEffect
#define SDL_GameControllerSetLED SDL_SetGamepadLED
#define SDL_GameControllerSetPlayerIndex SDL_SetGamepadPlayerIndex
#define SDL_GameControllerSetSensorEnabled SDL_SetGamepadSensorEnabled
#define SDL_GameControllerType SDL_GamepadType
#define SDL_GameControllerUpdate SDL_UpdateGamepads
#define SDL_INIT_GAMECONTROLLER SDL_INIT_GAMEPAD
#define SDL_IsGameController SDL_IsGamepad

/* ##SDL_guid.h */
#define SDL_GUIDFromString SDL_StringToGUID

/* ##SDL_haptic.h */
#define SDL_HapticClose SDL_CloseHaptic
#define SDL_HapticDestroyEffect SDL_DestroyHapticEffect
#define SDL_HapticGetEffectStatus SDL_GetHapticEffectStatus
#define SDL_HapticNewEffect SDL_CreateHapticEffect
#define SDL_HapticNumAxes SDL_GetNumHapticAxes
#define SDL_HapticNumEffects SDL_GetMaxHapticEffects
#define SDL_HapticNumEffectsPlaying SDL_GetMaxHapticEffectsPlaying
#define SDL_HapticOpen SDL_OpenHaptic
#define SDL_HapticOpenFromJoystick SDL_OpenHapticFromJoystick
#define SDL_HapticOpenFromMouse SDL_OpenHapticFromMouse
#define SDL_HapticPause SDL_PauseHaptic
#define SDL_HapticQuery SDL_GetHapticFeatures
#define SDL_HapticRumbleInit SDL_InitHapticRumble
#define SDL_HapticRumblePlay SDL_PlayHapticRumble
#define SDL_HapticRumbleStop SDL_StopHapticRumble
#define SDL_HapticRunEffect SDL_RunHapticEffect
#define SDL_HapticSetAutocenter SDL_SetHapticAutocenter
#define SDL_HapticSetGain SDL_SetHapticGain
#define SDL_HapticStopAll SDL_StopHapticEffects
#define SDL_HapticStopEffect SDL_StopHapticEffect
#define SDL_HapticUnpause SDL_ResumeHaptic
#define SDL_HapticUpdateEffect SDL_UpdateHapticEffect
#define SDL_JoystickIsHaptic SDL_IsJoystickHaptic
#define SDL_MouseIsHaptic SDL_IsMouseHaptic

/* ##SDL_hints.h */
#define SDL_DelHintCallback SDL_RemoveHintCallback
#define SDL_HINT_ALLOW_TOPMOST SDL_HINT_WINDOW_ALLOW_TOPMOST
#define SDL_HINT_DIRECTINPUT_ENABLED SDL_HINT_JOYSTICK_DIRECTINPUT
#define SDL_HINT_GDK_TEXTINPUT_DEFAULT SDL_HINT_GDK_TEXTINPUT_DEFAULT_TEXT
#define SDL_HINT_JOYSTICK_GAMECUBE_RUMBLE_BRAKE SDL_HINT_JOYSTICK_HIDAPI_GAMECUBE_RUMBLE_BRAKE
#define SDL_HINT_JOYSTICK_HIDAPI_PS4_RUMBLE SDL_HINT_JOYSTICK_ENHANCED_REPORTS
#define SDL_HINT_JOYSTICK_HIDAPI_PS5_RUMBLE SDL_HINT_JOYSTICK_ENHANCED_REPORTS
#define SDL_HINT_LINUX_DIGITAL_HATS SDL_HINT_JOYSTICK_LINUX_DIGITAL_HATS
#define SDL_HINT_LINUX_HAT_DEADZONES SDL_HINT_JOYSTICK_LINUX_HAT_DEADZONES
#define SDL_HINT_LINUX_JOYSTICK_CLASSIC SDL_HINT_JOYSTICK_LINUX_CLASSIC
#define SDL_HINT_LINUX_JOYSTICK_DEADZONES SDL_HINT_JOYSTICK_LINUX_DEADZONES

/* ##SDL_joystick.h */
#define SDL_JOYSTICK_TYPE_GAMECONTROLLER SDL_JOYSTICK_TYPE_GAMEPAD
#define SDL_JoystickAttachVirtualEx SDL_AttachVirtualJoystick
#define SDL_JoystickClose SDL_CloseJoystick
#define SDL_JoystickDetachVirtual SDL_DetachVirtualJoystick
#define SDL_JoystickFromInstanceID SDL_GetJoystickFromID
#define SDL_JoystickFromPlayerIndex SDL_GetJoystickFromPlayerIndex
#define SDL_JoystickGUID SDL_GUID
#define SDL_JoystickGetAttached SDL_JoystickConnected
#define SDL_JoystickGetAxis SDL_GetJoystickAxis
#define SDL_JoystickGetAxisInitialState SDL_GetJoystickAxisInitialState
#define SDL_JoystickGetBall SDL_GetJoystickBall
#define SDL_JoystickGetButton SDL_GetJoystickButton
#define SDL_JoystickGetFirmwareVersion SDL_GetJoystickFirmwareVersion
#define SDL_JoystickGetGUID SDL_GetJoystickGUID
#define SDL_JoystickGetGUIDFromString SDL_StringToGUID
#define SDL_JoystickGetHat SDL_GetJoystickHat
#define SDL_JoystickGetPlayerIndex SDL_GetJoystickPlayerIndex
#define SDL_JoystickGetProduct SDL_GetJoystickProduct
#define SDL_JoystickGetProductVersion SDL_GetJoystickProductVersion
#define SDL_JoystickGetSerial SDL_GetJoystickSerial
#define SDL_JoystickGetType SDL_GetJoystickType
#define SDL_JoystickGetVendor SDL_GetJoystickVendor
#define SDL_JoystickInstanceID SDL_GetJoystickID
#define SDL_JoystickIsVirtual SDL_IsJoystickVirtual
#define SDL_JoystickName SDL_GetJoystickName
#define SDL_JoystickNumAxes SDL_GetNumJoystickAxes
#define SDL_JoystickNumBalls SDL_GetNumJoystickBalls
#define SDL_JoystickNumButtons SDL_GetNumJoystickButtons
#define SDL_JoystickNumHats SDL_GetNumJoystickHats
#define SDL_JoystickOpen SDL_OpenJoystick
#define SDL_JoystickPath SDL_GetJoystickPath
#define SDL_JoystickRumble SDL_RumbleJoystick
#define SDL_JoystickRumbleTriggers SDL_RumbleJoystickTriggers
#define SDL_JoystickSendEffect SDL_SendJoystickEffect
#define SDL_JoystickSetLED SDL_SetJoystickLED
#define SDL_JoystickSetPlayerIndex SDL_SetJoystickPlayerIndex
#define SDL_JoystickSetVirtualAxis SDL_SetJoystickVirtualAxis
#define SDL_JoystickSetVirtualButton SDL_SetJoystickVirtualButton
#define SDL_JoystickSetVirtualHat SDL_SetJoystickVirtualHat
#define SDL_JoystickUpdate SDL_UpdateJoysticks

/* ##SDL_keyboard.h */
#define SDL_IsScreenKeyboardShown SDL_ScreenKeyboardShown
#define SDL_IsTextInputActive SDL_TextInputActive

/* ##SDL_keycode.h */
#define KMOD_ALT SDL_KMOD_ALT
#define KMOD_CAPS SDL_KMOD_CAPS
#define KMOD_CTRL SDL_KMOD_CTRL
#define KMOD_GUI SDL_KMOD_GUI
#define KMOD_LALT SDL_KMOD_LALT
#define KMOD_LCTRL SDL_KMOD_LCTRL
#define KMOD_LGUI SDL_KMOD_LGUI
#define KMOD_LSHIFT SDL_KMOD_LSHIFT
#define KMOD_MODE SDL_KMOD_MODE
#define KMOD_NONE SDL_KMOD_NONE
#define KMOD_NUM SDL_KMOD_NUM
#define KMOD_RALT SDL_KMOD_RALT
#define KMOD_RCTRL SDL_KMOD_RCTRL
#define KMOD_RGUI SDL_KMOD_RGUI
#define KMOD_RSHIFT SDL_KMOD_RSHIFT
#define KMOD_SCROLL SDL_KMOD_SCROLL
#define KMOD_SHIFT SDL_KMOD_SHIFT
#define SDLK_AUDIOFASTFORWARD SDLK_MEDIA_FAST_FORWARD
#define SDLK_AUDIOMUTE SDLK_MUTE
#define SDLK_AUDIONEXT SDLK_MEDIA_NEXT_TRACK
#define SDLK_AUDIOPLAY SDLK_MEDIA_PLAY
#define SDLK_AUDIOPREV SDLK_MEDIA_PREVIOUS_TRACK
#define SDLK_AUDIOREWIND SDLK_MEDIA_REWIND
#define SDLK_AUDIOSTOP SDLK_MEDIA_STOP
#define SDLK_BACKQUOTE SDLK_GRAVE
#define SDLK_EJECT SDLK_MEDIA_EJECT
#define SDLK_MEDIASELECT SDLK_MEDIA_SELECT
#define SDLK_QUOTE SDLK_APOSTROPHE
#define SDLK_QUOTEDBL SDLK_DBLAPOSTROPHE
#define SDLK_a SDLK_A
#define SDLK_b SDLK_B
#define SDLK_c SDLK_C
#define SDLK_d SDLK_D
#define SDLK_e SDLK_E
#define SDLK_f SDLK_F
#define SDLK_g SDLK_G
#define SDLK_h SDLK_H
#define SDLK_i SDLK_I
#define SDLK_j SDLK_J
#define SDLK_k SDLK_K
#define SDLK_l SDLK_L
#define SDLK_m SDLK_M
#define SDLK_n SDLK_N
#define SDLK_o SDLK_O
#define SDLK_p SDLK_P
#define SDLK_q SDLK_Q
#define SDLK_r SDLK_R
#define SDLK_s SDLK_S
#define SDLK_t SDLK_T
#define SDLK_u SDLK_U
#define SDLK_v SDLK_V
#define SDLK_w SDLK_W
#define SDLK_x SDLK_X
#define SDLK_y SDLK_Y
#define SDLK_z SDLK_Z

/* ##SDL_log.h */
#define SDL_LogGetOutputFunction SDL_GetLogOutputFunction
#define SDL_LogGetPriority SDL_GetLogPriority
#define SDL_LogResetPriorities SDL_ResetLogPriorities
#define SDL_LogSetAllPriority SDL_SetLogPriorities
#define SDL_LogSetOutputFunction SDL_SetLogOutputFunction
#define SDL_LogSetPriority SDL_SetLogPriority
#define SDL_NUM_LOG_PRIORITIES SDL_LOG_PRIORITY_COUNT

/* ##SDL_messagebox.h */
#define SDL_MESSAGEBOX_COLOR_MAX SDL_MESSAGEBOX_COLOR_COUNT

/* ##SDL_mouse.h */
#define SDL_BUTTON SDL_BUTTON_MASK
#define SDL_FreeCursor SDL_DestroyCursor
#define SDL_NUM_SYSTEM_CURSORS SDL_SYSTEM_CURSOR_COUNT
#define SDL_SYSTEM_CURSOR_ARROW SDL_SYSTEM_CURSOR_DEFAULT
#define SDL_SYSTEM_CURSOR_HAND SDL_SYSTEM_CURSOR_POINTER
#define SDL_SYSTEM_CURSOR_IBEAM SDL_SYSTEM_CURSOR_TEXT
#define SDL_SYSTEM_CURSOR_NO SDL_SYSTEM_CURSOR_NOT_ALLOWED
#define SDL_SYSTEM_CURSOR_SIZEALL SDL_SYSTEM_CURSOR_MOVE
#define SDL_SYSTEM_CURSOR_SIZENESW SDL_SYSTEM_CURSOR_NESW_RESIZE
#define SDL_SYSTEM_CURSOR_SIZENS SDL_SYSTEM_CURSOR_NS_RESIZE
#define SDL_SYSTEM_CURSOR_SIZENWSE SDL_SYSTEM_CURSOR_NWSE_RESIZE
#define SDL_SYSTEM_CURSOR_SIZEWE SDL_SYSTEM_CURSOR_EW_RESIZE
#define SDL_SYSTEM_CURSOR_WAITARROW SDL_SYSTEM_CURSOR_PROGRESS
#define SDL_SYSTEM_CURSOR_WINDOW_BOTTOM SDL_SYSTEM_CURSOR_S_RESIZE
#define SDL_SYSTEM_CURSOR_WINDOW_BOTTOMLEFT SDL_SYSTEM_CURSOR_SW_RESIZE
#define SDL_SYSTEM_CURSOR_WINDOW_BOTTOMRIGHT SDL_SYSTEM_CURSOR_SE_RESIZE
#define SDL_SYSTEM_CURSOR_WINDOW_LEFT SDL_SYSTEM_CURSOR_W_RESIZE
#define SDL_SYSTEM_CURSOR_WINDOW_RIGHT SDL_SYSTEM_CURSOR_E_RESIZE
#define SDL_SYSTEM_CURSOR_WINDOW_TOP SDL_SYSTEM_CURSOR_N_RESIZE
#define SDL_SYSTEM_CURSOR_WINDOW_TOPLEFT SDL_SYSTEM_CURSOR_NW_RESIZE
#define SDL_SYSTEM_CURSOR_WINDOW_TOPRIGHT SDL_SYSTEM_CURSOR_NE_RESIZE

/* ##SDL_mutex.h */
#define SDL_CondBroadcast SDL_BroadcastCondition
#define SDL_CondSignal SDL_SignalCondition
#define SDL_CondWait SDL_WaitCondition
#define SDL_CondWaitTimeout SDL_WaitConditionTimeout
#define SDL_CreateCond SDL_CreateCondition
#define SDL_DestroyCond SDL_DestroyCondition
#define SDL_SemPost SDL_SignalSemaphore
#define SDL_SemTryWait SDL_TryWaitSemaphore
#define SDL_SemValue SDL_GetSemaphoreValue
#define SDL_SemWait SDL_WaitSemaphore
#define SDL_SemWaitTimeout SDL_WaitSemaphoreTimeout

/* ##SDL_mutex.h */
#define SDL_cond SDL_Condition
#define SDL_mutex SDL_Mutex
#define SDL_sem SDL_Semaphore

/* ##SDL_pixels.h */
#define SDL_AllocFormat SDL_GetPixelFormatDetails
#define SDL_AllocPalette SDL_CreatePalette
#define SDL_Colour SDL_Color
#define SDL_FreePalette SDL_DestroyPalette
#define SDL_MasksToPixelFormatEnum SDL_GetPixelFormatForMasks
#define SDL_PIXELFORMAT_BGR444 SDL_PIXELFORMAT_XBGR4444
#define SDL_PIXELFORMAT_BGR555 SDL_PIXELFORMAT_XBGR1555
#define SDL_PIXELFORMAT_BGR888 SDL_PIXELFORMAT_XBGR8888
#define SDL_PIXELFORMAT_RGB444 SDL_PIXELFORMAT_XRGB4444
#define SDL_PIXELFORMAT_RGB555 SDL_PIXELFORMAT_XRGB1555
#define SDL_PIXELFORMAT_RGB888 SDL_PIXELFORMAT_XRGB8888
#define SDL_PixelFormatEnumToMasks SDL_GetMasksForPixelFormat

/* ##SDL_rect.h */
#define SDL_EncloseFPoints SDL_GetRectEnclosingPointsFloat
#define SDL_EnclosePoints SDL_GetRectEnclosingPoints
#define SDL_FRectEmpty SDL_RectEmptyFloat
#define SDL_FRectEquals SDL_RectsEqualFloat
#define SDL_FRectEqualsEpsilon SDL_RectsEqualEpsilon
#define SDL_HasIntersection SDL_HasRectIntersection
#define SDL_HasIntersectionF SDL_HasRectIntersectionFloat
#define SDL_IntersectFRect SDL_GetRectIntersectionFloat
#define SDL_IntersectFRectAndLine SDL_GetRectAndLineIntersectionFloat
#define SDL_IntersectRect SDL_GetRectIntersection
#define SDL_IntersectRectAndLine SDL_GetRectAndLineIntersection
#define SDL_PointInFRect SDL_PointInRectFloat
#define SDL_RectEquals SDL_RectsEqual
#define SDL_UnionFRect SDL_GetRectUnionFloat
#define SDL_UnionRect SDL_GetRectUnion

/* ##SDL_render.h */
#define SDL_GetRendererOutputSize SDL_GetCurrentRenderOutputSize
#define SDL_RenderCopy SDL_RenderTexture
#define SDL_RenderCopyEx SDL_RenderTextureRotated
#define SDL_RenderCopyExF SDL_RenderTextureRotated
#define SDL_RenderCopyF SDL_RenderTexture
#define SDL_RenderDrawLine SDL_RenderLine
#define SDL_RenderDrawLineF SDL_RenderLine
#define SDL_RenderDrawLines SDL_RenderLines
#define SDL_RenderDrawLinesF SDL_RenderLines
#define SDL_RenderDrawPoint SDL_RenderPoint
#define SDL_RenderDrawPointF SDL_RenderPoint
#define SDL_RenderDrawPoints SDL_RenderPoints
#define SDL_RenderDrawPointsF SDL_RenderPoints
#define SDL_RenderDrawRect SDL_RenderRect
#define SDL_RenderDrawRectF SDL_RenderRect
#define SDL_RenderDrawRects SDL_RenderRects
#define SDL_RenderDrawRectsF SDL_RenderRects
#define SDL_RenderFillRectF SDL_RenderFillRect
#define SDL_RenderFillRectsF SDL_RenderFillRects
#define SDL_RendererFlip SDL_FlipMode
#define SDL_RenderFlush SDL_FlushRenderer
#define SDL_RenderGetClipRect SDL_GetRenderClipRect
#define SDL_RenderGetLogicalSize SDL_GetRenderLogicalPresentation
#define SDL_RenderGetMetalCommandEncoder SDL_GetRenderMetalCommandEncoder
#define SDL_RenderGetMetalLayer SDL_GetRenderMetalLayer
#define SDL_RenderGetScale SDL_GetRenderScale
#define SDL_RenderGetViewport SDL_GetRenderViewport
#define SDL_RenderGetWindow SDL_GetRenderWindow
#define SDL_RenderIsClipEnabled SDL_RenderClipEnabled
#define SDL_RenderLogicalToWindow SDL_RenderCoordinatesToWindow
#define SDL_RenderSetClipRect SDL_SetRenderClipRect
#define SDL_RenderSetLogicalSize SDL_SetRenderLogicalPresentation
#define SDL_RenderSetScale SDL_SetRenderScale
#define SDL_RenderSetVSync SDL_SetRenderVSync
#define SDL_RenderSetViewport SDL_SetRenderViewport
#define SDL_RenderWindowToLogical SDL_RenderCoordinatesFromWindow
#define SDL_ScaleModeLinear SDL_SCALEMODE_LINEAR
#define SDL_ScaleModeNearest SDL_SCALEMODE_NEAREST

/* ##SDL_rwops.h */
#define RW_SEEK_CUR SDL_IO_SEEK_CUR
#define RW_SEEK_END SDL_IO_SEEK_END
#define RW_SEEK_SET SDL_IO_SEEK_SET
#define SDL_RWFromConstMem SDL_IOFromConstMem
#define SDL_RWFromFile SDL_IOFromFile
#define SDL_RWFromMem SDL_IOFromMem
#define SDL_RWclose SDL_CloseIO
#define SDL_RWops SDL_IOStream
#define SDL_RWread SDL_ReadIO
#define SDL_RWseek SDL_SeekIO
#define SDL_RWsize SDL_GetIOSize
#define SDL_RWtell SDL_TellIO
#define SDL_RWwrite SDL_WriteIO
#define SDL_ReadBE16 SDL_ReadU16BE
#define SDL_ReadBE32 SDL_ReadU32BE
#define SDL_ReadBE64 SDL_ReadU64BE
#define SDL_ReadLE16 SDL_ReadU16LE
#define SDL_ReadLE32 SDL_ReadU32LE
#define SDL_ReadLE64 SDL_ReadU64LE
#define SDL_WriteBE16 SDL_WriteU16BE
#define SDL_WriteBE32 SDL_WriteU32BE
#define SDL_WriteBE64 SDL_WriteU64BE
#define SDL_WriteLE16 SDL_WriteU16LE
#define SDL_WriteLE32 SDL_WriteU32LE
#define SDL_WriteLE64 SDL_WriteU64LE

/* ##SDL_scancode.h */
#define SDL_NUM_SCANCODES SDL_SCANCODE_COUNT
#define SDL_SCANCODE_AUDIOFASTFORWARD SDL_SCANCODE_MEDIA_FAST_FORWARD
#define SDL_SCANCODE_AUDIOMUTE SDL_SCANCODE_MUTE
#define SDL_SCANCODE_AUDIONEXT SDL_SCANCODE_MEDIA_NEXT_TRACK
#define SDL_SCANCODE_AUDIOPLAY SDL_SCANCODE_MEDIA_PLAY
#define SDL_SCANCODE_AUDIOPREV SDL_SCANCODE_MEDIA_PREVIOUS_TRACK
#define SDL_SCANCODE_AUDIOREWIND SDL_SCANCODE_MEDIA_REWIND
#define SDL_SCANCODE_AUDIOSTOP SDL_SCANCODE_MEDIA_STOP
#define SDL_SCANCODE_EJECT SDL_SCANCODE_MEDIA_EJECT
#define SDL_SCANCODE_MEDIASELECT SDL_SCANCODE_MEDIA_SELECT

/* ##SDL_sensor.h */
#define SDL_SensorClose SDL_CloseSensor
#define SDL_SensorFromInstanceID SDL_GetSensorFromID
#define SDL_SensorGetData SDL_GetSensorData
#define SDL_SensorGetInstanceID SDL_GetSensorID
#define SDL_SensorGetName SDL_GetSensorName
#define SDL_SensorGetNonPortableType SDL_GetSensorNonPortableType
#define SDL_SensorGetType SDL_GetSensorType
#define SDL_SensorOpen SDL_OpenSensor
#define SDL_SensorUpdate SDL_UpdateSensors

/* ##SDL_stdinc.h */
#define SDL_FALSE false
#define SDL_TABLESIZE SDL_arraysize
#define SDL_TRUE true
#define SDL_bool bool
#define SDL_size_add_overflow SDL_size_add_check_overflow
#define SDL_size_mul_overflow SDL_size_mul_check_overflow
#define SDL_strtokr SDL_strtok_r

/* ##SDL_surface.h */
#define SDL_BlitScaled SDL_BlitSurfaceScaled
#define SDL_ConvertSurfaceFormat SDL_ConvertSurface
#define SDL_FillRect SDL_FillSurfaceRect
#define SDL_FillRects SDL_FillSurfaceRects
#define SDL_FreeSurface SDL_DestroySurface
#define SDL_GetClipRect SDL_GetSurfaceClipRect
#define SDL_GetColorKey SDL_GetSurfaceColorKey
#define SDL_HasColorKey SDL_SurfaceHasColorKey
#define SDL_HasSurfaceRLE SDL_SurfaceHasRLE
#define SDL_LoadBMP_RW SDL_LoadBMP_IO
#define SDL_LowerBlit SDL_BlitSurfaceUnchecked
#define SDL_LowerBlitScaled SDL_BlitSurfaceUncheckedScaled
#define SDL_PREALLOC SDL_SURFACE_PREALLOCATED
#define SDL_SIMD_ALIGNED SDL_SURFACE_SIMD_ALIGNED
#define SDL_SaveBMP_RW SDL_SaveBMP_IO
#define SDL_SetClipRect SDL_SetSurfaceClipRect
#define SDL_SetColorKey SDL_SetSurfaceColorKey
#define SDL_UpperBlit SDL_BlitSurface
#define SDL_UpperBlitScaled SDL_BlitSurfaceScaled

/* ##SDL_system.h */
#define SDL_AndroidBackButton SDL_SendAndroidBackButton
#define SDL_AndroidGetActivity SDL_GetAndroidActivity
#define SDL_AndroidGetExternalStoragePath SDL_GetAndroidExternalStoragePath
#define SDL_AndroidGetExternalStorageState SDL_GetAndroidExternalStorageState
#define SDL_AndroidGetInternalStoragePath SDL_GetAndroidInternalStoragePath
#define SDL_AndroidGetJNIEnv SDL_GetAndroidJNIEnv
#define SDL_AndroidRequestPermission SDL_RequestAndroidPermission
#define SDL_AndroidRequestPermissionCallback SDL_RequestAndroidPermissionCallback
#define SDL_AndroidSendMessage SDL_SendAndroidMessage
#define SDL_AndroidShowToast SDL_ShowAndroidToast
#define SDL_DXGIGetOutputInfo SDL_GetDXGIOutputInfo
#define SDL_Direct3D9GetAdapterIndex SDL_GetDirect3D9AdapterIndex
#define SDL_GDKGetDefaultUser SDL_GetGDKDefaultUser
#define SDL_GDKGetTaskQueue SDL_GetGDKTaskQueue
#define SDL_LinuxSetThreadPriority SDL_SetLinuxThreadPriority
#define SDL_LinuxSetThreadPriorityAndPolicy SDL_SetLinuxThreadPriorityAndPolicy
#define SDL_OnApplicationDidBecomeActive SDL_OnApplicationDidEnterForeground
#define SDL_OnApplicationWillResignActive SDL_OnApplicationWillEnterBackground
#define SDL_iOSSetAnimationCallback SDL_SetiOSAnimationCallback
#define SDL_iOSSetEventPump SDL_SetiOSEventPump
#define SDL_iPhoneSetAnimationCallback SDL_SetiOSAnimationCallback
#define SDL_iPhoneSetEventPump SDL_SetiOSEventPump

/* ##SDL_thread.h */
#define SDL_SetThreadPriority SDL_SetCurrentThreadPriority
#define SDL_TLSCleanup SDL_CleanupTLS
#define SDL_TLSGet SDL_GetTLS
#define SDL_TLSSet SDL_SetTLS
#define SDL_threadID SDL_ThreadID

/* ##SDL_timer.h */
#define SDL_GetTicks64 SDL_GetTicks

/* ##SDL_version.h */
#define SDL_COMPILEDVERSION SDL_VERSION
#define SDL_PATCHLEVEL SDL_MICRO_VERSION

/* ##SDL_video.h */
#define SDL_GL_DeleteContext SDL_GL_DestroyContext
#define SDL_GLattr SDL_GLAttr
#define SDL_GLcontextFlag SDL_GLContextFlag
#define SDL_GLcontextReleaseFlag SDL_GLContextReleaseFlag
#define SDL_GLprofile SDL_GLProfile
#define SDL_GetClosestDisplayMode SDL_GetClosestFullscreenDisplayMode
#define SDL_GetDisplayOrientation SDL_GetCurrentDisplayOrientation
#define SDL_GetPointDisplayIndex SDL_GetDisplayForPoint
#define SDL_GetRectDisplayIndex SDL_GetDisplayForRect
#define SDL_GetWindowDisplayIndex SDL_GetDisplayForWindow
#define SDL_GetWindowDisplayMode SDL_GetWindowFullscreenMode
#define SDL_HasWindowSurface SDL_WindowHasSurface
#define SDL_IsScreenSaverEnabled SDL_ScreenSaverEnabled
#define SDL_SetWindowDisplayMode SDL_SetWindowFullscreenMode
#define SDL_WINDOW_ALLOW_HIGHDPI SDL_WINDOW_HIGH_PIXEL_DENSITY
#define SDL_WINDOW_INPUT_GRABBED SDL_WINDOW_MOUSE_GRABBED
#define SDL_WINDOW_SKIP_TASKBAR SDL_WINDOW_UTILITY

#elif !defined(SDL_DISABLE_OLD_NAMES)

/* ##SDL_atomic.h */
#define SDL_AtomicAdd SDL_AtomicAdd_renamed_SDL_AddAtomicInt
#define SDL_AtomicCAS SDL_AtomicCAS_renamed_SDL_CompareAndSwapAtomicInt
#define SDL_AtomicCASPtr SDL_AtomicCASPtr_renamed_SDL_CompareAndSwapAtomicPointer
#define SDL_AtomicGet SDL_AtomicGet_renamed_SDL_GetAtomicInt
#define SDL_AtomicGetPtr SDL_AtomicGetPtr_renamed_SDL_GetAtomicPointer
#define SDL_AtomicLock SDL_AtomicLock_renamed_SDL_LockSpinlock
#define SDL_AtomicSet SDL_AtomicSet_renamed_SDL_SetAtomicInt
#define SDL_AtomicSetPtr SDL_AtomicSetPtr_renamed_SDL_SetAtomicPointer
#define SDL_AtomicTryLock SDL_AtomicTryLock_renamed_SDL_TryLockSpinlock
#define SDL_AtomicUnlock SDL_AtomicUnlock_renamed_SDL_UnlockSpinlock
#define SDL_atomic_t SDL_atomic_t_renamed_SDL_AtomicInt

/* ##SDL_audio.h */
#define AUDIO_F32 AUDIO_F32_renamed_SDL_AUDIO_F32LE
#define AUDIO_F32LSB AUDIO_F32LSB_renamed_SDL_AUDIO_F32LE
#define AUDIO_F32MSB AUDIO_F32MSB_renamed_SDL_AUDIO_F32BE
#define AUDIO_F32SYS AUDIO_F32SYS_renamed_SDL_AUDIO_F32
#define AUDIO_S16 AUDIO_S16_renamed_SDL_AUDIO_S16LE
#define AUDIO_S16LSB AUDIO_S16LSB_renamed_SDL_AUDIO_S16LE
#define AUDIO_S16MSB AUDIO_S16MSB_renamed_SDL_AUDIO_S16BE
#define AUDIO_S16SYS AUDIO_S16SYS_renamed_SDL_AUDIO_S16
#define AUDIO_S32 AUDIO_S32_renamed_SDL_AUDIO_S32LE
#define AUDIO_S32LSB AUDIO_S32LSB_renamed_SDL_AUDIO_S32LE
#define AUDIO_S32MSB AUDIO_S32MSB_renamed_SDL_AUDIO_S32BE
#define AUDIO_S32SYS AUDIO_S32SYS_renamed_SDL_AUDIO_S32
#define AUDIO_S8 AUDIO_S8_renamed_SDL_AUDIO_S8
#define AUDIO_U8 AUDIO_U8_renamed_SDL_AUDIO_U8
#define SDL_AudioStreamAvailable SDL_AudioStreamAvailable_renamed_SDL_GetAudioStreamAvailable
#define SDL_AudioStreamClear SDL_AudioStreamClear_renamed_SDL_ClearAudioStream
#define SDL_AudioStreamFlush SDL_AudioStreamFlush_renamed_SDL_FlushAudioStream
#define SDL_AudioStreamGet SDL_AudioStreamGet_renamed_SDL_GetAudioStreamData
#define SDL_AudioStreamPut SDL_AudioStreamPut_renamed_SDL_PutAudioStreamData
#define SDL_FreeAudioStream SDL_FreeAudioStream_renamed_SDL_DestroyAudioStream
#define SDL_FreeWAV SDL_FreeWAV_renamed_SDL_free
#define SDL_LoadWAV_RW SDL_LoadWAV_RW_renamed_SDL_LoadWAV_IO
#define SDL_MixAudioFormat SDL_MixAudioFormat_renamed_SDL_MixAudio
#define SDL_NewAudioStream SDL_NewAudioStream_renamed_SDL_CreateAudioStream

/* ##SDL_cpuinfo.h */
#define SDL_GetCPUCount SDL_GetCPUCount_renamed_SDL_GetNumLogicalCPUCores
#define SDL_SIMDGetAlignment SDL_SIMDGetAlignment_renamed_SDL_GetSIMDAlignment

/* ##SDL_endian.h */
#define SDL_SwapBE16 SDL_SwapBE16_renamed_SDL_Swap16BE
#define SDL_SwapBE32 SDL_SwapBE32_renamed_SDL_Swap32BE
#define SDL_SwapBE64 SDL_SwapBE64_renamed_SDL_Swap64BE
#define SDL_SwapLE16 SDL_SwapLE16_renamed_SDL_Swap16LE
#define SDL_SwapLE32 SDL_SwapLE32_renamed_SDL_Swap32LE
#define SDL_SwapLE64 SDL_SwapLE64_renamed_SDL_Swap64LE

/* ##SDL_events.h */
#define SDL_APP_DIDENTERBACKGROUND SDL_APP_DIDENTERBACKGROUND_renamed_SDL_EVENT_DID_ENTER_BACKGROUND
#define SDL_APP_DIDENTERFOREGROUND SDL_APP_DIDENTERFOREGROUND_renamed_SDL_EVENT_DID_ENTER_FOREGROUND
#define SDL_APP_LOWMEMORY SDL_APP_LOWMEMORY_renamed_SDL_EVENT_LOW_MEMORY
#define SDL_APP_TERMINATING SDL_APP_TERMINATING_renamed_SDL_EVENT_TERMINATING
#define SDL_APP_WILLENTERBACKGROUND SDL_APP_WILLENTERBACKGROUND_renamed_SDL_EVENT_WILL_ENTER_BACKGROUND
#define SDL_APP_WILLENTERFOREGROUND SDL_APP_WILLENTERFOREGROUND_renamed_SDL_EVENT_WILL_ENTER_FOREGROUND
#define SDL_AUDIODEVICEADDED SDL_AUDIODEVICEADDED_renamed_SDL_EVENT_AUDIO_DEVICE_ADDED
#define SDL_AUDIODEVICEREMOVED SDL_AUDIODEVICEREMOVED_renamed_SDL_EVENT_AUDIO_DEVICE_REMOVED
#define SDL_CLIPBOARDUPDATE SDL_CLIPBOARDUPDATE_renamed_SDL_EVENT_CLIPBOARD_UPDATE
#define SDL_CONTROLLERAXISMOTION SDL_CONTROLLERAXISMOTION_renamed_SDL_EVENT_GAMEPAD_AXIS_MOTION
#define SDL_CONTROLLERBUTTONDOWN SDL_CONTROLLERBUTTONDOWN_renamed_SDL_EVENT_GAMEPAD_BUTTON_DOWN
#define SDL_CONTROLLERBUTTONUP SDL_CONTROLLERBUTTONUP_renamed_SDL_EVENT_GAMEPAD_BUTTON_UP
#define SDL_CONTROLLERDEVICEADDED SDL_CONTROLLERDEVICEADDED_renamed_SDL_EVENT_GAMEPAD_ADDED
#define SDL_CONTROLLERDEVICEREMAPPED SDL_CONTROLLERDEVICEREMAPPED_renamed_SDL_EVENT_GAMEPAD_REMAPPED
#define SDL_CONTROLLERDEVICEREMOVED SDL_CONTROLLERDEVICEREMOVED_renamed_SDL_EVENT_GAMEPAD_REMOVED
#define SDL_CONTROLLERSENSORUPDATE SDL_CONTROLLERSENSORUPDATE_renamed_SDL_EVENT_GAMEPAD_SENSOR_UPDATE
#define SDL_CONTROLLERSTEAMHANDLEUPDATED SDL_CONTROLLERSTEAMHANDLEUPDATED_renamed_SDL_EVENT_GAMEPAD_STEAM_HANDLE_UPDATED
#define SDL_CONTROLLERTOUCHPADDOWN SDL_CONTROLLERTOUCHPADDOWN_renamed_SDL_EVENT_GAMEPAD_TOUCHPAD_DOWN
#define SDL_CONTROLLERTOUCHPADMOTION SDL_CONTROLLERTOUCHPADMOTION_renamed_SDL_EVENT_GAMEPAD_TOUCHPAD_MOTION
#define SDL_CONTROLLERTOUCHPADUP SDL_CONTROLLERTOUCHPADUP_renamed_SDL_EVENT_GAMEPAD_TOUCHPAD_UP
#define SDL_ControllerAxisEvent SDL_ControllerAxisEvent_renamed_SDL_GamepadAxisEvent
#define SDL_ControllerButtonEvent SDL_ControllerButtonEvent_renamed_SDL_GamepadButtonEvent
#define SDL_ControllerDeviceEvent SDL_ControllerDeviceEvent_renamed_SDL_GamepadDeviceEvent
#define SDL_ControllerSensorEvent SDL_ControllerSensorEvent_renamed_SDL_GamepadSensorEvent
#define SDL_ControllerTouchpadEvent SDL_ControllerTouchpadEvent_renamed_SDL_GamepadTouchpadEvent
#define SDL_DISPLAYEVENT_CONNECTED SDL_DISPLAYEVENT_CONNECTED_renamed_SDL_EVENT_DISPLAY_ADDED
#define SDL_DISPLAYEVENT_DISCONNECTED SDL_DISPLAYEVENT_DISCONNECTED_renamed_SDL_EVENT_DISPLAY_REMOVED
#define SDL_DISPLAYEVENT_MOVED SDL_DISPLAYEVENT_MOVED_renamed_SDL_EVENT_DISPLAY_MOVED
#define SDL_DISPLAYEVENT_ORIENTATION SDL_DISPLAYEVENT_ORIENTATION_renamed_SDL_EVENT_DISPLAY_ORIENTATION
#define SDL_DROPBEGIN SDL_DROPBEGIN_renamed_SDL_EVENT_DROP_BEGIN
#define SDL_DROPCOMPLETE SDL_DROPCOMPLETE_renamed_SDL_EVENT_DROP_COMPLETE
#define SDL_DROPFILE SDL_DROPFILE_renamed_SDL_EVENT_DROP_FILE
#define SDL_DROPTEXT SDL_DROPTEXT_renamed_SDL_EVENT_DROP_TEXT
#define SDL_DelEventWatch SDL_DelEventWatch_renamed_SDL_RemoveEventWatch
#define SDL_FINGERDOWN SDL_FINGERDOWN_renamed_SDL_EVENT_FINGER_DOWN
#define SDL_FINGERMOTION SDL_FINGERMOTION_renamed_SDL_EVENT_FINGER_MOTION
#define SDL_FINGERUP SDL_FINGERUP_renamed_SDL_EVENT_FINGER_UP
#define SDL_FIRSTEVENT SDL_FIRSTEVENT_renamed_SDL_EVENT_FIRST
#define SDL_JOYAXISMOTION SDL_JOYAXISMOTION_renamed_SDL_EVENT_JOYSTICK_AXIS_MOTION
#define SDL_JOYBATTERYUPDATED SDL_JOYBATTERYUPDATED_renamed_SDL_EVENT_JOYSTICK_BATTERY_UPDATED
#define SDL_JOYBUTTONDOWN SDL_JOYBUTTONDOWN_renamed_SDL_EVENT_JOYSTICK_BUTTON_DOWN
#define SDL_JOYBUTTONUP SDL_JOYBUTTONUP_renamed_SDL_EVENT_JOYSTICK_BUTTON_UP
#define SDL_JOYDEVICEADDED SDL_JOYDEVICEADDED_renamed_SDL_EVENT_JOYSTICK_ADDED
#define SDL_JOYDEVICEREMOVED SDL_JOYDEVICEREMOVED_renamed_SDL_EVENT_JOYSTICK_REMOVED
#define SDL_JOYBALLMOTION SDL_JOYBALLMOTION_renamed_SDL_EVENT_JOYSTICK_BALL_MOTION
#define SDL_JOYHATMOTION SDL_JOYHATMOTION_renamed_SDL_EVENT_JOYSTICK_HAT_MOTION
#define SDL_KEYDOWN SDL_KEYDOWN_renamed_SDL_EVENT_KEY_DOWN
#define SDL_KEYMAPCHANGED SDL_KEYMAPCHANGED_renamed_SDL_EVENT_KEYMAP_CHANGED
#define SDL_KEYUP SDL_KEYUP_renamed_SDL_EVENT_KEY_UP
#define SDL_LASTEVENT SDL_LASTEVENT_renamed_SDL_EVENT_LAST
#define SDL_LOCALECHANGED SDL_LOCALECHANGED_renamed_SDL_EVENT_LOCALE_CHANGED
#define SDL_MOUSEBUTTONDOWN SDL_MOUSEBUTTONDOWN_renamed_SDL_EVENT_MOUSE_BUTTON_DOWN
#define SDL_MOUSEBUTTONUP SDL_MOUSEBUTTONUP_renamed_SDL_EVENT_MOUSE_BUTTON_UP
#define SDL_MOUSEMOTION SDL_MOUSEMOTION_renamed_SDL_EVENT_MOUSE_MOTION
#define SDL_MOUSEWHEEL SDL_MOUSEWHEEL_renamed_SDL_EVENT_MOUSE_WHEEL
#define SDL_POLLSENTINEL SDL_POLLSENTINEL_renamed_SDL_EVENT_POLL_SENTINEL
#define SDL_QUIT SDL_QUIT_renamed_SDL_EVENT_QUIT
#define SDL_RENDER_DEVICE_RESET SDL_RENDER_DEVICE_RESET_renamed_SDL_EVENT_RENDER_DEVICE_RESET
#define SDL_RENDER_TARGETS_RESET SDL_RENDER_TARGETS_RESET_renamed_SDL_EVENT_RENDER_TARGETS_RESET
#define SDL_SENSORUPDATE SDL_SENSORUPDATE_renamed_SDL_EVENT_SENSOR_UPDATE
#define SDL_TEXTEDITING SDL_TEXTEDITING_renamed_SDL_EVENT_TEXT_EDITING
#define SDL_TEXTEDITING_EXT SDL_TEXTEDITING_EXT_renamed_SDL_EVENT_TEXT_EDITING_EXT
#define SDL_TEXTINPUT SDL_TEXTINPUT_renamed_SDL_EVENT_TEXT_INPUT
#define SDL_USEREVENT SDL_USEREVENT_renamed_SDL_EVENT_USER
#define SDL_WINDOWEVENT_CLOSE SDL_WINDOWEVENT_CLOSE_renamed_SDL_EVENT_WINDOW_CLOSE_REQUESTED
#define SDL_WINDOWEVENT_DISPLAY_CHANGED SDL_WINDOWEVENT_DISPLAY_CHANGED_renamed_SDL_EVENT_WINDOW_DISPLAY_CHANGED
#define SDL_WINDOWEVENT_ENTER SDL_WINDOWEVENT_ENTER_renamed_SDL_EVENT_WINDOW_MOUSE_ENTER
#define SDL_WINDOWEVENT_EXPOSED SDL_WINDOWEVENT_EXPOSED_renamed_SDL_EVENT_WINDOW_EXPOSED
#define SDL_WINDOWEVENT_FOCUS_GAINED SDL_WINDOWEVENT_FOCUS_GAINED_renamed_SDL_EVENT_WINDOW_FOCUS_GAINED
#define SDL_WINDOWEVENT_FOCUS_LOST SDL_WINDOWEVENT_FOCUS_LOST_renamed_SDL_EVENT_WINDOW_FOCUS_LOST
#define SDL_WINDOWEVENT_HIDDEN SDL_WINDOWEVENT_HIDDEN_renamed_SDL_EVENT_WINDOW_HIDDEN
#define SDL_WINDOWEVENT_HIT_TEST SDL_WINDOWEVENT_HIT_TEST_renamed_SDL_EVENT_WINDOW_HIT_TEST
#define SDL_WINDOWEVENT_ICCPROF_CHANGED SDL_WINDOWEVENT_ICCPROF_CHANGED_renamed_SDL_EVENT_WINDOW_ICCPROF_CHANGED
#define SDL_WINDOWEVENT_LEAVE SDL_WINDOWEVENT_LEAVE_renamed_SDL_EVENT_WINDOW_MOUSE_LEAVE
#define SDL_WINDOWEVENT_MAXIMIZED SDL_WINDOWEVENT_MAXIMIZED_renamed_SDL_EVENT_WINDOW_MAXIMIZED
#define SDL_WINDOWEVENT_MINIMIZED SDL_WINDOWEVENT_MINIMIZED_renamed_SDL_EVENT_WINDOW_MINIMIZED
#define SDL_WINDOWEVENT_MOVED SDL_WINDOWEVENT_MOVED_renamed_SDL_EVENT_WINDOW_MOVED
#define SDL_WINDOWEVENT_RESIZED SDL_WINDOWEVENT_RESIZED_renamed_SDL_EVENT_WINDOW_RESIZED
#define SDL_WINDOWEVENT_RESTORED SDL_WINDOWEVENT_RESTORED_renamed_SDL_EVENT_WINDOW_RESTORED
#define SDL_WINDOWEVENT_SHOWN SDL_WINDOWEVENT_SHOWN_renamed_SDL_EVENT_WINDOW_SHOWN
#define SDL_WINDOWEVENT_SIZE_CHANGED SDL_WINDOWEVENT_SIZE_CHANGED_renamed_SDL_EVENT_WINDOW_PIXEL_SIZE_CHANGED
#define SDL_eventaction SDL_eventaction_renamed_SDL_EventAction

/* ##SDL_gamecontroller.h */
#define SDL_CONTROLLER_AXIS_INVALID SDL_CONTROLLER_AXIS_INVALID_renamed_SDL_GAMEPAD_AXIS_INVALID
#define SDL_CONTROLLER_AXIS_LEFTX SDL_CONTROLLER_AXIS_LEFTX_renamed_SDL_GAMEPAD_AXIS_LEFTX
#define SDL_CONTROLLER_AXIS_LEFTY SDL_CONTROLLER_AXIS_LEFTY_renamed_SDL_GAMEPAD_AXIS_LEFTY
#define SDL_CONTROLLER_AXIS_MAX SDL_CONTROLLER_AXIS_MAX_renamed_SDL_GAMEPAD_AXIS_COUNT
#define SDL_CONTROLLER_AXIS_RIGHTX SDL_CONTROLLER_AXIS_RIGHTX_renamed_SDL_GAMEPAD_AXIS_RIGHTX
#define SDL_CONTROLLER_AXIS_RIGHTY SDL_CONTROLLER_AXIS_RIGHTY_renamed_SDL_GAMEPAD_AXIS_RIGHTY
#define SDL_CONTROLLER_AXIS_TRIGGERLEFT SDL_CONTROLLER_AXIS_TRIGGERLEFT_renamed_SDL_GAMEPAD_AXIS_LEFT_TRIGGER
#define SDL_CONTROLLER_AXIS_TRIGGERRIGHT SDL_CONTROLLER_AXIS_TRIGGERRIGHT_renamed_SDL_GAMEPAD_AXIS_RIGHT_TRIGGER
#define SDL_CONTROLLER_BINDTYPE_AXIS SDL_CONTROLLER_BINDTYPE_AXIS_renamed_SDL_GAMEPAD_BINDTYPE_AXIS
#define SDL_CONTROLLER_BINDTYPE_BUTTON SDL_CONTROLLER_BINDTYPE_BUTTON_renamed_SDL_GAMEPAD_BINDTYPE_BUTTON
#define SDL_CONTROLLER_BINDTYPE_HAT SDL_CONTROLLER_BINDTYPE_HAT_renamed_SDL_GAMEPAD_BINDTYPE_HAT
#define SDL_CONTROLLER_BINDTYPE_NONE SDL_CONTROLLER_BINDTYPE_NONE_renamed_SDL_GAMEPAD_BINDTYPE_NONE
#define SDL_CONTROLLER_BUTTON_A SDL_CONTROLLER_BUTTON_A_renamed_SDL_GAMEPAD_BUTTON_SOUTH
#define SDL_CONTROLLER_BUTTON_B SDL_CONTROLLER_BUTTON_B_renamed_SDL_GAMEPAD_BUTTON_EAST
#define SDL_CONTROLLER_BUTTON_BACK SDL_CONTROLLER_BUTTON_BACK_renamed_SDL_GAMEPAD_BUTTON_BACK
#define SDL_CONTROLLER_BUTTON_DPAD_DOWN SDL_CONTROLLER_BUTTON_DPAD_DOWN_renamed_SDL_GAMEPAD_BUTTON_DPAD_DOWN
#define SDL_CONTROLLER_BUTTON_DPAD_LEFT SDL_CONTROLLER_BUTTON_DPAD_LEFT_renamed_SDL_GAMEPAD_BUTTON_DPAD_LEFT
#define SDL_CONTROLLER_BUTTON_DPAD_RIGHT SDL_CONTROLLER_BUTTON_DPAD_RIGHT_renamed_SDL_GAMEPAD_BUTTON_DPAD_RIGHT
#define SDL_CONTROLLER_BUTTON_DPAD_UP SDL_CONTROLLER_BUTTON_DPAD_UP_renamed_SDL_GAMEPAD_BUTTON_DPAD_UP
#define SDL_CONTROLLER_BUTTON_GUIDE SDL_CONTROLLER_BUTTON_GUIDE_renamed_SDL_GAMEPAD_BUTTON_GUIDE
#define SDL_CONTROLLER_BUTTON_INVALID SDL_CONTROLLER_BUTTON_INVALID_renamed_SDL_GAMEPAD_BUTTON_INVALID
#define SDL_CONTROLLER_BUTTON_LEFTSHOULDER SDL_CONTROLLER_BUTTON_LEFTSHOULDER_renamed_SDL_GAMEPAD_BUTTON_LEFT_SHOULDER
#define SDL_CONTROLLER_BUTTON_LEFTSTICK SDL_CONTROLLER_BUTTON_LEFTSTICK_renamed_SDL_GAMEPAD_BUTTON_LEFT_STICK
#define SDL_CONTROLLER_BUTTON_MAX SDL_CONTROLLER_BUTTON_MAX_renamed_SDL_GAMEPAD_BUTTON_COUNT
#define SDL_CONTROLLER_BUTTON_MISC1 SDL_CONTROLLER_BUTTON_MISC1_renamed_SDL_GAMEPAD_BUTTON_MISC1
#define SDL_CONTROLLER_BUTTON_PADDLE1 SDL_CONTROLLER_BUTTON_PADDLE1_renamed_SDL_GAMEPAD_BUTTON_RIGHT_PADDLE1
#define SDL_CONTROLLER_BUTTON_PADDLE2 SDL_CONTROLLER_BUTTON_PADDLE2_renamed_SDL_GAMEPAD_BUTTON_LEFT_PADDLE1
#define SDL_CONTROLLER_BUTTON_PADDLE3 SDL_CONTROLLER_BUTTON_PADDLE3_renamed_SDL_GAMEPAD_BUTTON_RIGHT_PADDLE2
#define SDL_CONTROLLER_BUTTON_PADDLE4 SDL_CONTROLLER_BUTTON_PADDLE4_renamed_SDL_GAMEPAD_BUTTON_LEFT_PADDLE2
#define SDL_CONTROLLER_BUTTON_RIGHTSHOULDER SDL_CONTROLLER_BUTTON_RIGHTSHOULDER_renamed_SDL_GAMEPAD_BUTTON_RIGHT_SHOULDER
#define SDL_CONTROLLER_BUTTON_RIGHTSTICK SDL_CONTROLLER_BUTTON_RIGHTSTICK_renamed_SDL_GAMEPAD_BUTTON_RIGHT_STICK
#define SDL_CONTROLLER_BUTTON_START SDL_CONTROLLER_BUTTON_START_renamed_SDL_GAMEPAD_BUTTON_START
#define SDL_CONTROLLER_BUTTON_TOUCHPAD SDL_CONTROLLER_BUTTON_TOUCHPAD_renamed_SDL_GAMEPAD_BUTTON_TOUCHPAD
#define SDL_CONTROLLER_BUTTON_X SDL_CONTROLLER_BUTTON_X_renamed_SDL_GAMEPAD_BUTTON_WEST
#define SDL_CONTROLLER_BUTTON_Y SDL_CONTROLLER_BUTTON_Y_renamed_SDL_GAMEPAD_BUTTON_NORTH
#define SDL_CONTROLLER_TYPE_NINTENDO_SWITCH_JOYCON_LEFT SDL_CONTROLLER_TYPE_NINTENDO_SWITCH_JOYCON_LEFT_renamed_SDL_GAMEPAD_TYPE_NINTENDO_SWITCH_JOYCON_LEFT
#define SDL_CONTROLLER_TYPE_NINTENDO_SWITCH_JOYCON_PAIR SDL_CONTROLLER_TYPE_NINTENDO_SWITCH_JOYCON_PAIR_renamed_SDL_GAMEPAD_TYPE_NINTENDO_SWITCH_JOYCON_PAIR
#define SDL_CONTROLLER_TYPE_NINTENDO_SWITCH_JOYCON_RIGHT SDL_CONTROLLER_TYPE_NINTENDO_SWITCH_JOYCON_RIGHT_renamed_SDL_GAMEPAD_TYPE_NINTENDO_SWITCH_JOYCON_RIGHT
#define SDL_CONTROLLER_TYPE_NINTENDO_SWITCH_PRO SDL_CONTROLLER_TYPE_NINTENDO_SWITCH_PRO_renamed_SDL_GAMEPAD_TYPE_NINTENDO_SWITCH_PRO
#define SDL_CONTROLLER_TYPE_PS3 SDL_CONTROLLER_TYPE_PS3_renamed_SDL_GAMEPAD_TYPE_PS3
#define SDL_CONTROLLER_TYPE_PS4 SDL_CONTROLLER_TYPE_PS4_renamed_SDL_GAMEPAD_TYPE_PS4
#define SDL_CONTROLLER_TYPE_PS5 SDL_CONTROLLER_TYPE_PS5_renamed_SDL_GAMEPAD_TYPE_PS5
#define SDL_CONTROLLER_TYPE_UNKNOWN SDL_CONTROLLER_TYPE_UNKNOWN_renamed_SDL_GAMEPAD_TYPE_STANDARD
#define SDL_CONTROLLER_TYPE_VIRTUAL SDL_CONTROLLER_TYPE_VIRTUAL_renamed_SDL_GAMEPAD_TYPE_VIRTUAL
#define SDL_CONTROLLER_TYPE_XBOX360 SDL_CONTROLLER_TYPE_XBOX360_renamed_SDL_GAMEPAD_TYPE_XBOX360
#define SDL_CONTROLLER_TYPE_XBOXONE SDL_CONTROLLER_TYPE_XBOXONE_renamed_SDL_GAMEPAD_TYPE_XBOXONE
#define SDL_GameController SDL_GameController_renamed_SDL_Gamepad
#define SDL_GameControllerAddMapping SDL_GameControllerAddMapping_renamed_SDL_AddGamepadMapping
#define SDL_GameControllerAddMappingsFromFile SDL_GameControllerAddMappingsFromFile_renamed_SDL_AddGamepadMappingsFromFile
#define SDL_GameControllerAddMappingsFromRW SDL_GameControllerAddMappingsFromRW_renamed_SDL_AddGamepadMappingsFromIO
#define SDL_GameControllerAxis SDL_GameControllerAxis_renamed_SDL_GamepadAxis
#define SDL_GameControllerBindType SDL_GameControllerBindType_renamed_SDL_GamepadBindingType
#define SDL_GameControllerButton SDL_GameControllerButton_renamed_SDL_GamepadButton
#define SDL_GameControllerClose SDL_GameControllerClose_renamed_SDL_CloseGamepad
#define SDL_GameControllerFromInstanceID SDL_GameControllerFromInstanceID_renamed_SDL_GetGamepadFromID
#define SDL_GameControllerFromPlayerIndex SDL_GameControllerFromPlayerIndex_renamed_SDL_GetGamepadFromPlayerIndex
#define SDL_GameControllerGetAppleSFSymbolsNameForAxis SDL_GameControllerGetAppleSFSymbolsNameForAxis_renamed_SDL_GetGamepadAppleSFSymbolsNameForAxis
#define SDL_GameControllerGetAppleSFSymbolsNameForButton SDL_GameControllerGetAppleSFSymbolsNameForButton_renamed_SDL_GetGamepadAppleSFSymbolsNameForButton
#define SDL_GameControllerGetAttached SDL_GameControllerGetAttached_renamed_SDL_GamepadConnected
#define SDL_GameControllerGetAxis SDL_GameControllerGetAxis_renamed_SDL_GetGamepadAxis
#define SDL_GameControllerGetAxisFromString SDL_GameControllerGetAxisFromString_renamed_SDL_GetGamepadAxisFromString
#define SDL_GameControllerGetButton SDL_GameControllerGetButton_renamed_SDL_GetGamepadButton
#define SDL_GameControllerGetButtonFromString SDL_GameControllerGetButtonFromString_renamed_SDL_GetGamepadButtonFromString
#define SDL_GameControllerGetFirmwareVersion SDL_GameControllerGetFirmwareVersion_renamed_SDL_GetGamepadFirmwareVersion
#define SDL_GameControllerGetJoystick SDL_GameControllerGetJoystick_renamed_SDL_GetGamepadJoystick
#define SDL_GameControllerGetNumTouchpadFingers SDL_GameControllerGetNumTouchpadFingers_renamed_SDL_GetNumGamepadTouchpadFingers
#define SDL_GameControllerGetNumTouchpads SDL_GameControllerGetNumTouchpads_renamed_SDL_GetNumGamepadTouchpads
#define SDL_GameControllerGetPlayerIndex SDL_GameControllerGetPlayerIndex_renamed_SDL_GetGamepadPlayerIndex
#define SDL_GameControllerGetProduct SDL_GameControllerGetProduct_renamed_SDL_GetGamepadProduct
#define SDL_GameControllerGetProductVersion SDL_GameControllerGetProductVersion_renamed_SDL_GetGamepadProductVersion
#define SDL_GameControllerGetSensorData SDL_GameControllerGetSensorData_renamed_SDL_GetGamepadSensorData
#define SDL_GameControllerGetSensorDataRate SDL_GameControllerGetSensorDataRate_renamed_SDL_GetGamepadSensorDataRate
#define SDL_GameControllerGetSerial SDL_GameControllerGetSerial_renamed_SDL_GetGamepadSerial
#define SDL_GameControllerGetSteamHandle SDL_GameControllerGetSteamHandle_renamed_SDL_GetGamepadSteamHandle
#define SDL_GameControllerGetStringForAxis SDL_GameControllerGetStringForAxis_renamed_SDL_GetGamepadStringForAxis
#define SDL_GameControllerGetStringForButton SDL_GameControllerGetStringForButton_renamed_SDL_GetGamepadStringForButton
#define SDL_GameControllerGetTouchpadFinger SDL_GameControllerGetTouchpadFinger_renamed_SDL_GetGamepadTouchpadFinger
#define SDL_GameControllerGetType SDL_GameControllerGetType_renamed_SDL_GetGamepadType
#define SDL_GameControllerGetVendor SDL_GameControllerGetVendor_renamed_SDL_GetGamepadVendor
#define SDL_GameControllerHasAxis SDL_GameControllerHasAxis_renamed_SDL_GamepadHasAxis
#define SDL_GameControllerHasButton SDL_GameControllerHasButton_renamed_SDL_GamepadHasButton
#define SDL_GameControllerHasSensor SDL_GameControllerHasSensor_renamed_SDL_GamepadHasSensor
#define SDL_GameControllerIsSensorEnabled SDL_GameControllerIsSensorEnabled_renamed_SDL_GamepadSensorEnabled
#define SDL_GameControllerMapping SDL_GameControllerMapping_renamed_SDL_GetGamepadMapping
#define SDL_GameControllerMappingForDeviceIndex SDL_GameControllerMappingForDeviceIndex_renamed_SDL_GetGamepadMappingForDeviceIndex
#define SDL_GameControllerMappingForGUID SDL_GameControllerMappingForGUID_renamed_SDL_GetGamepadMappingForGUID
#define SDL_GameControllerName SDL_GameControllerName_renamed_SDL_GetGamepadName
#define SDL_GameControllerOpen SDL_GameControllerOpen_renamed_SDL_OpenGamepad
#define SDL_GameControllerPath SDL_GameControllerPath_renamed_SDL_GetGamepadPath
#define SDL_GameControllerRumble SDL_GameControllerRumble_renamed_SDL_RumbleGamepad
#define SDL_GameControllerRumbleTriggers SDL_GameControllerRumbleTriggers_renamed_SDL_RumbleGamepadTriggers
#define SDL_GameControllerSendEffect SDL_GameControllerSendEffect_renamed_SDL_SendGamepadEffect
#define SDL_GameControllerSetLED SDL_GameControllerSetLED_renamed_SDL_SetGamepadLED
#define SDL_GameControllerSetPlayerIndex SDL_GameControllerSetPlayerIndex_renamed_SDL_SetGamepadPlayerIndex
#define SDL_GameControllerSetSensorEnabled SDL_GameControllerSetSensorEnabled_renamed_SDL_SetGamepadSensorEnabled
#define SDL_GameControllerType SDL_GameControllerType_renamed_SDL_GamepadType
#define SDL_GameControllerUpdate SDL_GameControllerUpdate_renamed_SDL_UpdateGamepads
#define SDL_INIT_GAMECONTROLLER SDL_INIT_GAMECONTROLLER_renamed_SDL_INIT_GAMEPAD
#define SDL_IsGameController SDL_IsGameController_renamed_SDL_IsGamepad

/* ##SDL_guid.h */
#define SDL_GUIDFromString SDL_GUIDFromString_renamed_SDL_StringToGUID

/* ##SDL_haptic.h */
#define SDL_HapticClose SDL_HapticClose_renamed_SDL_CloseHaptic
#define SDL_HapticDestroyEffect SDL_HapticDestroyEffect_renamed_SDL_DestroyHapticEffect
#define SDL_HapticGetEffectStatus SDL_HapticGetEffectStatus_renamed_SDL_GetHapticEffectStatus
#define SDL_HapticNewEffect SDL_HapticNewEffect_renamed_SDL_CreateHapticEffect
#define SDL_HapticNumAxes SDL_HapticNumAxes_renamed_SDL_GetNumHapticAxes
#define SDL_HapticNumEffects SDL_HapticNumEffects_renamed_SDL_GetMaxHapticEffects
#define SDL_HapticNumEffectsPlaying SDL_HapticNumEffectsPlaying_renamed_SDL_GetMaxHapticEffectsPlaying
#define SDL_HapticOpen SDL_HapticOpen_renamed_SDL_OpenHaptic
#define SDL_HapticOpenFromJoystick SDL_HapticOpenFromJoystick_renamed_SDL_OpenHapticFromJoystick
#define SDL_HapticOpenFromMouse SDL_HapticOpenFromMouse_renamed_SDL_OpenHapticFromMouse
#define SDL_HapticPause SDL_HapticPause_renamed_SDL_PauseHaptic
#define SDL_HapticQuery SDL_HapticQuery_renamed_SDL_GetHapticFeatures
#define SDL_HapticRumbleInit SDL_HapticRumbleInit_renamed_SDL_InitHapticRumble
#define SDL_HapticRumblePlay SDL_HapticRumblePlay_renamed_SDL_PlayHapticRumble
#define SDL_HapticRumbleStop SDL_HapticRumbleStop_renamed_SDL_StopHapticRumble
#define SDL_HapticRunEffect SDL_HapticRunEffect_renamed_SDL_RunHapticEffect
#define SDL_HapticSetAutocenter SDL_HapticSetAutocenter_renamed_SDL_SetHapticAutocenter
#define SDL_HapticSetGain SDL_HapticSetGain_renamed_SDL_SetHapticGain
#define SDL_HapticStopAll SDL_HapticStopAll_renamed_SDL_StopHapticEffects
#define SDL_HapticStopEffect SDL_HapticStopEffect_renamed_SDL_StopHapticEffect
#define SDL_HapticUnpause SDL_HapticUnpause_renamed_SDL_ResumeHaptic
#define SDL_HapticUpdateEffect SDL_HapticUpdateEffect_renamed_SDL_UpdateHapticEffect
#define SDL_JoystickIsHaptic SDL_JoystickIsHaptic_renamed_SDL_IsJoystickHaptic
#define SDL_MouseIsHaptic SDL_MouseIsHaptic_renamed_SDL_IsMouseHaptic

/* ##SDL_hints.h */
#define SDL_DelHintCallback SDL_DelHintCallback_renamed_SDL_RemoveHintCallback
#define SDL_HINT_ALLOW_TOPMOST SDL_HINT_ALLOW_TOPMOST_renamed_SDL_HINT_WINDOW_ALLOW_TOPMOST
#define SDL_HINT_DIRECTINPUT_ENABLED SDL_HINT_DIRECTINPUT_ENABLED_renamed_SDL_HINT_JOYSTICK_DIRECTINPUT
#define SDL_HINT_GDK_TEXTINPUT_DEFAULT SDL_HINT_GDK_TEXTINPUT_DEFAULT_renamed_SDL_HINT_GDK_TEXTINPUT_DEFAULT_TEXT
#define SDL_HINT_JOYSTICK_GAMECUBE_RUMBLE_BRAKE SDL_HINT_JOYSTICK_GAMECUBE_RUMBLE_BRAKE_renamed_SDL_HINT_JOYSTICK_HIDAPI_GAMECUBE_RUMBLE_BRAKE
#define SDL_HINT_JOYSTICK_HIDAPI_PS4_RUMBLE SDL_HINT_JOYSTICK_HIDAPI_PS4_RUMBLE_renamed_SDL_HINT_JOYSTICK_ENHANCED_REPORTS
#define SDL_HINT_JOYSTICK_HIDAPI_PS5_RUMBLE SDL_HINT_JOYSTICK_HIDAPI_PS5_RUMBLE_renamed_SDL_HINT_JOYSTICK_ENHANCED_REPORTS
#define SDL_HINT_LINUX_DIGITAL_HATS SDL_HINT_LINUX_DIGITAL_HATS_renamed_SDL_HINT_JOYSTICK_LINUX_DIGITAL_HATS
#define SDL_HINT_LINUX_HAT_DEADZONES SDL_HINT_LINUX_HAT_DEADZONES_renamed_SDL_HINT_JOYSTICK_LINUX_HAT_DEADZONES
#define SDL_HINT_LINUX_JOYSTICK_CLASSIC SDL_HINT_LINUX_JOYSTICK_CLASSIC_renamed_SDL_HINT_JOYSTICK_LINUX_CLASSIC
#define SDL_HINT_LINUX_JOYSTICK_DEADZONES SDL_HINT_LINUX_JOYSTICK_DEADZONES_renamed_SDL_HINT_JOYSTICK_LINUX_DEADZONES

/* ##SDL_joystick.h */
#define SDL_JOYSTICK_TYPE_GAMECONTROLLER SDL_JOYSTICK_TYPE_GAMECONTROLLER_renamed_SDL_JOYSTICK_TYPE_GAMEPAD
#define SDL_JoystickAttachVirtualEx SDL_JoystickAttachVirtualEx_renamed_SDL_AttachVirtualJoystick
#define SDL_JoystickClose SDL_JoystickClose_renamed_SDL_CloseJoystick
#define SDL_JoystickDetachVirtual SDL_JoystickDetachVirtual_renamed_SDL_DetachVirtualJoystick
#define SDL_JoystickFromInstanceID SDL_JoystickFromInstanceID_renamed_SDL_GetJoystickFromID
#define SDL_JoystickFromPlayerIndex SDL_JoystickFromPlayerIndex_renamed_SDL_GetJoystickFromPlayerIndex
#define SDL_JoystickGUID SDL_JoystickGUID_renamed_SDL_GUID
#define SDL_JoystickGetAttached SDL_JoystickGetAttached_renamed_SDL_JoystickConnected
#define SDL_JoystickGetAxis SDL_JoystickGetAxis_renamed_SDL_GetJoystickAxis
#define SDL_JoystickGetAxisInitialState SDL_JoystickGetAxisInitialState_renamed_SDL_GetJoystickAxisInitialState
#define SDL_JoystickGetBall SDL_JoystickGetBall_renamed_SDL_GetJoystickBall
#define SDL_JoystickGetButton SDL_JoystickGetButton_renamed_SDL_GetJoystickButton
#define SDL_JoystickGetFirmwareVersion SDL_JoystickGetFirmwareVersion_renamed_SDL_GetJoystickFirmwareVersion
#define SDL_JoystickGetGUID SDL_JoystickGetGUID_renamed_SDL_GetJoystickGUID
#define SDL_JoystickGetGUIDFromString SDL_JoystickGetGUIDFromString_renamed_SDL_GUIDFromString
#define SDL_JoystickGetHat SDL_JoystickGetHat_renamed_SDL_GetJoystickHat
#define SDL_JoystickGetPlayerIndex SDL_JoystickGetPlayerIndex_renamed_SDL_GetJoystickPlayerIndex
#define SDL_JoystickGetProduct SDL_JoystickGetProduct_renamed_SDL_GetJoystickProduct
#define SDL_JoystickGetProductVersion SDL_JoystickGetProductVersion_renamed_SDL_GetJoystickProductVersion
#define SDL_JoystickGetSerial SDL_JoystickGetSerial_renamed_SDL_GetJoystickSerial
#define SDL_JoystickGetType SDL_JoystickGetType_renamed_SDL_GetJoystickType
#define SDL_JoystickGetVendor SDL_JoystickGetVendor_renamed_SDL_GetJoystickVendor
#define SDL_JoystickInstanceID SDL_JoystickInstanceID_renamed_SDL_GetJoystickID
#define SDL_JoystickIsVirtual SDL_JoystickIsVirtual_renamed_SDL_IsJoystickVirtual
#define SDL_JoystickName SDL_JoystickName_renamed_SDL_GetJoystickName
#define SDL_JoystickNumAxes SDL_JoystickNumAxes_renamed_SDL_GetNumJoystickAxes
#define SDL_JoystickNumBalls SDL_JoystickNumBalls_renamed_SDL_GetNumJoystickBalls
#define SDL_JoystickNumButtons SDL_JoystickNumButtons_renamed_SDL_GetNumJoystickButtons
#define SDL_JoystickNumHats SDL_JoystickNumHats_renamed_SDL_GetNumJoystickHats
#define SDL_JoystickOpen SDL_JoystickOpen_renamed_SDL_OpenJoystick
#define SDL_JoystickPath SDL_JoystickPath_renamed_SDL_GetJoystickPath
#define SDL_JoystickRumble SDL_JoystickRumble_renamed_SDL_RumbleJoystick
#define SDL_JoystickRumbleTriggers SDL_JoystickRumbleTriggers_renamed_SDL_RumbleJoystickTriggers
#define SDL_JoystickSendEffect SDL_JoystickSendEffect_renamed_SDL_SendJoystickEffect
#define SDL_JoystickSetLED SDL_JoystickSetLED_renamed_SDL_SetJoystickLED
#define SDL_JoystickSetPlayerIndex SDL_JoystickSetPlayerIndex_renamed_SDL_SetJoystickPlayerIndex
#define SDL_JoystickSetVirtualAxis SDL_JoystickSetVirtualAxis_renamed_SDL_SetJoystickVirtualAxis
#define SDL_JoystickSetVirtualButton SDL_JoystickSetVirtualButton_renamed_SDL_SetJoystickVirtualButton
#define SDL_JoystickSetVirtualHat SDL_JoystickSetVirtualHat_renamed_SDL_SetJoystickVirtualHat
#define SDL_JoystickUpdate SDL_JoystickUpdate_renamed_SDL_UpdateJoysticks

/* ##SDL_keyboard.h */
#define SDL_IsScreenKeyboardShown SDL_IsScreenKeyboardShown_renamed_SDL_ScreenKeyboardShown
#define SDL_IsTextInputActive SDL_IsTextInputActive_renamed_SDL_TextInputActive

/* ##SDL_keycode.h */
#define KMOD_ALT KMOD_ALT_renamed_SDL_KMOD_ALT
#define KMOD_CAPS KMOD_CAPS_renamed_SDL_KMOD_CAPS
#define KMOD_CTRL KMOD_CTRL_renamed_SDL_KMOD_CTRL
#define KMOD_GUI KMOD_GUI_renamed_SDL_KMOD_GUI
#define KMOD_LALT KMOD_LALT_renamed_SDL_KMOD_LALT
#define KMOD_LCTRL KMOD_LCTRL_renamed_SDL_KMOD_LCTRL
#define KMOD_LGUI KMOD_LGUI_renamed_SDL_KMOD_LGUI
#define KMOD_LSHIFT KMOD_LSHIFT_renamed_SDL_KMOD_LSHIFT
#define KMOD_MODE KMOD_MODE_renamed_SDL_KMOD_MODE
#define KMOD_NONE KMOD_NONE_renamed_SDL_KMOD_NONE
#define KMOD_NUM KMOD_NUM_renamed_SDL_KMOD_NUM
#define KMOD_RALT KMOD_RALT_renamed_SDL_KMOD_RALT
#define KMOD_RCTRL KMOD_RCTRL_renamed_SDL_KMOD_RCTRL
#define KMOD_RGUI KMOD_RGUI_renamed_SDL_KMOD_RGUI
#define KMOD_RSHIFT KMOD_RSHIFT_renamed_SDL_KMOD_RSHIFT
#define KMOD_SCROLL KMOD_SCROLL_renamed_SDL_KMOD_SCROLL
#define KMOD_SHIFT KMOD_SHIFT_renamed_SDL_KMOD_SHIFT
#define SDLK_AUDIOFASTFORWARD SDLK_AUDIOFASTFORWARD_renamed_SDLK_MEDIA_FAST_FORWARD
#define SDLK_AUDIOMUTE SDLK_AUDIOMUTE_renamed_SDLK_MUTE
#define SDLK_AUDIONEXT SDLK_AUDIONEXT_renamed_SDLK_MEDIA_NEXT_TRACK
#define SDLK_AUDIOPLAY SDLK_AUDIOPLAY_renamed_SDLK_MEDIA_PLAY
#define SDLK_AUDIOPREV SDLK_AUDIOPREV_renamed_SDLK_MEDIA_PREVIOUS_TRACK
#define SDLK_AUDIOREWIND SDLK_AUDIOREWIND_renamed_SDLK_MEDIA_REWIND
#define SDLK_AUDIOSTOP SDLK_AUDIOSTOP_renamed_SDLK_MEDIA_STOP
#define SDLK_BACKQUOTE SDLK_BACKQUOTE_renamed_SDLK_GRAVE
#define SDLK_EJECT SDLK_EJECT_renamed_SDLK_MEDIA_EJECT
#define SDLK_MEDIASELECT SDLK_MEDIASELECT_renamed_SDLK_MEDIA_SELECT
#define SDLK_QUOTE SDLK_QUOTE_renamed_SDLK_APOSTROPHE
#define SDLK_QUOTEDBL SDLK_QUOTEDBL_renamed_SDLK_DBLAPOSTROPHE
#define SDLK_a SDLK_a_renamed_SDLK_A
#define SDLK_b SDLK_b_renamed_SDLK_B
#define SDLK_c SDLK_c_renamed_SDLK_C
#define SDLK_d SDLK_d_renamed_SDLK_D
#define SDLK_e SDLK_e_renamed_SDLK_E
#define SDLK_f SDLK_f_renamed_SDLK_F
#define SDLK_g SDLK_g_renamed_SDLK_G
#define SDLK_h SDLK_h_renamed_SDLK_H
#define SDLK_i SDLK_i_renamed_SDLK_I
#define SDLK_j SDLK_j_renamed_SDLK_J
#define SDLK_k SDLK_k_renamed_SDLK_K
#define SDLK_l SDLK_l_renamed_SDLK_L
#define SDLK_m SDLK_m_renamed_SDLK_M
#define SDLK_n SDLK_n_renamed_SDLK_N
#define SDLK_o SDLK_o_renamed_SDLK_O
#define SDLK_p SDLK_p_renamed_SDLK_P
#define SDLK_q SDLK_q_renamed_SDLK_Q
#define SDLK_r SDLK_r_renamed_SDLK_R
#define SDLK_s SDLK_s_renamed_SDLK_S
#define SDLK_t SDLK_t_renamed_SDLK_T
#define SDLK_u SDLK_u_renamed_SDLK_U
#define SDLK_v SDLK_v_renamed_SDLK_V
#define SDLK_w SDLK_w_renamed_SDLK_W
#define SDLK_x SDLK_x_renamed_SDLK_X
#define SDLK_y SDLK_y_renamed_SDLK_Y
#define SDLK_z SDLK_z_renamed_SDLK_Z

/* ##SDL_log.h */
#define SDL_LogGetOutputFunction SDL_LogGetOutputFunction_renamed_SDL_GetLogOutputFunction
#define SDL_LogGetPriority SDL_LogGetPriority_renamed_SDL_GetLogPriority
#define SDL_LogResetPriorities SDL_LogResetPriorities_renamed_SDL_ResetLogPriorities
#define SDL_LogSetAllPriority SDL_LogSetAllPriority_renamed_SDL_SetLogPriorities
#define SDL_LogSetOutputFunction SDL_LogSetOutputFunction_renamed_SDL_SetLogOutputFunction
#define SDL_LogSetPriority SDL_LogSetPriority_renamed_SDL_SetLogPriority
#define SDL_NUM_LOG_PRIORITIES SDL_NUM_LOG_PRIORITIES_renamed_SDL_LOG_PRIORITY_COUNT

/* ##SDL_messagebox.h */
#define SDL_MESSAGEBOX_COLOR_MAX SDL_MESSAGEBOX_COLOR_MAX_renamed_SDL_MESSAGEBOX_COLOR_COUNT

/* ##SDL_mouse.h */
#define SDL_BUTTON SDL_BUTTON_renamed_SDL_BUTTON_MASK
#define SDL_FreeCursor SDL_FreeCursor_renamed_SDL_DestroyCursor
#define SDL_NUM_SYSTEM_CURSORS SDL_NUM_SYSTEM_CURSORS_renamed_SDL_SYSTEM_CURSOR_COUNT
#define SDL_SYSTEM_CURSOR_ARROW SDL_SYSTEM_CURSOR_ARROW_renamed_SDL_SYSTEM_CURSOR_DEFAULT
#define SDL_SYSTEM_CURSOR_HAND SDL_SYSTEM_CURSOR_HAND_renamed_SDL_SYSTEM_CURSOR_POINTER
#define SDL_SYSTEM_CURSOR_IBEAM SDL_SYSTEM_CURSOR_IBEAM_renamed_SDL_SYSTEM_CURSOR_TEXT
#define SDL_SYSTEM_CURSOR_NO SDL_SYSTEM_CURSOR_NO_renamed_SDL_SYSTEM_CURSOR_NOT_ALLOWED
#define SDL_SYSTEM_CURSOR_SIZEALL SDL_SYSTEM_CURSOR_SIZEALL_renamed_SDL_SYSTEM_CURSOR_MOVE
#define SDL_SYSTEM_CURSOR_SIZENESW SDL_SYSTEM_CURSOR_SIZENESW_renamed_SDL_SYSTEM_CURSOR_NESW_RESIZE
#define SDL_SYSTEM_CURSOR_SIZENS SDL_SYSTEM_CURSOR_SIZENS_renamed_SDL_SYSTEM_CURSOR_NS_RESIZE
#define SDL_SYSTEM_CURSOR_SIZENWSE SDL_SYSTEM_CURSOR_SIZENWSE_renamed_SDL_SYSTEM_CURSOR_NWSE_RESIZE
#define SDL_SYSTEM_CURSOR_SIZEWE SDL_SYSTEM_CURSOR_SIZEWE_renamed_SDL_SYSTEM_CURSOR_EW_RESIZE
#define SDL_SYSTEM_CURSOR_WAITARROW SDL_SYSTEM_CURSOR_WAITARROW_renamed_SDL_SYSTEM_CURSOR_PROGRESS
#define SDL_SYSTEM_CURSOR_WINDOW_BOTTOM SDL_SYSTEM_CURSOR_WINDOW_BOTTOM_renamed_SDL_SYSTEM_CURSOR_S_RESIZE
#define SDL_SYSTEM_CURSOR_WINDOW_BOTTOMLEFT SDL_SYSTEM_CURSOR_WINDOW_BOTTOMLEFT_renamed_SDL_SYSTEM_CURSOR_SW_RESIZE
#define SDL_SYSTEM_CURSOR_WINDOW_BOTTOMRIGHT SDL_SYSTEM_CURSOR_WINDOW_BOTTOMRIGHT_renamed_SDL_SYSTEM_CURSOR_SE_RESIZE
#define SDL_SYSTEM_CURSOR_WINDOW_LEFT SDL_SYSTEM_CURSOR_WINDOW_LEFT_renamed_SDL_SYSTEM_CURSOR_W_RESIZE
#define SDL_SYSTEM_CURSOR_WINDOW_RIGHT SDL_SYSTEM_CURSOR_WINDOW_RIGHT_renamed_SDL_SYSTEM_CURSOR_E_RESIZE
#define SDL_SYSTEM_CURSOR_WINDOW_TOP SDL_SYSTEM_CURSOR_WINDOW_TOP_renamed_SDL_SYSTEM_CURSOR_N_RESIZE
#define SDL_SYSTEM_CURSOR_WINDOW_TOPLEFT SDL_SYSTEM_CURSOR_WINDOW_TOPLEFT_renamed_SDL_SYSTEM_CURSOR_NW_RESIZE
#define SDL_SYSTEM_CURSOR_WINDOW_TOPRIGHT SDL_SYSTEM_CURSOR_WINDOW_TOPRIGHT_renamed_SDL_SYSTEM_CURSOR_NE_RESIZE

/* ##SDL_mutex.h */
#define SDL_CondBroadcast SDL_CondBroadcast_renamed_SDL_BroadcastCondition
#define SDL_CondSignal SDL_CondSignal_renamed_SDL_SignalCondition
#define SDL_CondWait SDL_CondWait_renamed_SDL_WaitCondition
#define SDL_CondWaitTimeout SDL_CondWaitTimeout_renamed_SDL_WaitConditionTimeout
#define SDL_CreateCond SDL_CreateCond_renamed_SDL_CreateCondition
#define SDL_DestroyCond SDL_DestroyCond_renamed_SDL_DestroyCondition
#define SDL_SemPost SDL_SemPost_renamed_SDL_SignalSemaphore
#define SDL_SemTryWait SDL_SemTryWait_renamed_SDL_TryWaitSemaphore
#define SDL_SemValue SDL_SemValue_renamed_SDL_GetSemaphoreValue
#define SDL_SemWait SDL_SemWait_renamed_SDL_WaitSemaphore
#define SDL_SemWaitTimeout SDL_SemWaitTimeout_renamed_SDL_WaitSemaphoreTimeout

/* ##SDL_mutex.h */
#define SDL_cond SDL_cond_renamed_SDL_Condition
#define SDL_mutex SDL_mutex_renamed_SDL_Mutex
#define SDL_sem SDL_sem_renamed_SDL_Semaphore

/* ##SDL_pixels.h */
#define SDL_AllocFormat SDL_AllocFormat_renamed_SDL_GetPixelFormatDetails
#define SDL_AllocPalette SDL_AllocPalette_renamed_SDL_CreatePalette
#define SDL_Colour SDL_Colour_renamed_SDL_Color
#define SDL_FreePalette SDL_FreePalette_renamed_SDL_DestroyPalette
#define SDL_MasksToPixelFormatEnum SDL_MasksToPixelFormatEnum_renamed_SDL_GetPixelFormatForMasks
#define SDL_PIXELFORMAT_BGR444 SDL_PIXELFORMAT_BGR444_renamed_SDL_PIXELFORMAT_XBGR4444
#define SDL_PIXELFORMAT_BGR555 SDL_PIXELFORMAT_BGR555_renamed_SDL_PIXELFORMAT_XBGR1555
#define SDL_PIXELFORMAT_BGR888 SDL_PIXELFORMAT_BGR888_renamed_SDL_PIXELFORMAT_XBGR8888
#define SDL_PIXELFORMAT_RGB444 SDL_PIXELFORMAT_RGB444_renamed_SDL_PIXELFORMAT_XRGB4444
#define SDL_PIXELFORMAT_RGB555 SDL_PIXELFORMAT_RGB555_renamed_SDL_PIXELFORMAT_XRGB1555
#define SDL_PIXELFORMAT_RGB888 SDL_PIXELFORMAT_RGB888_renamed_SDL_PIXELFORMAT_XRGB8888
#define SDL_PixelFormatEnumToMasks SDL_PixelFormatEnumToMasks_renamed_SDL_GetMasksForPixelFormat

/* ##SDL_rect.h */
#define SDL_EncloseFPoints SDL_EncloseFPoints_renamed_SDL_GetRectEnclosingPointsFloat
#define SDL_EnclosePoints SDL_EnclosePoints_renamed_SDL_GetRectEnclosingPoints
#define SDL_FRectEmpty SDL_FRectEmpty_renamed_SDL_RectEmptyFloat
#define SDL_FRectEquals SDL_FRectEquals_renamed_SDL_RectsEqualFloat
#define SDL_FRectEqualsEpsilon SDL_FRectEqualsEpsilon_renamed_SDL_RectsEqualEpsilon
#define SDL_HasIntersection SDL_HasIntersection_renamed_SDL_HasRectIntersection
#define SDL_HasIntersectionF SDL_HasIntersectionF_renamed_SDL_HasRectIntersectionFloat
#define SDL_IntersectFRect SDL_IntersectFRect_renamed_SDL_GetRectIntersectionFloat
#define SDL_IntersectFRectAndLine SDL_IntersectFRectAndLine_renamed_SDL_GetRectAndLineIntersectionFloat
#define SDL_IntersectRect SDL_IntersectRect_renamed_SDL_GetRectIntersection
#define SDL_IntersectRectAndLine SDL_IntersectRectAndLine_renamed_SDL_GetRectAndLineIntersection
#define SDL_PointInFRect SDL_PointInFRect_renamed_SDL_PointInRectFloat
#define SDL_RectEquals SDL_RectEquals_renamed_SDL_RectsEqual
#define SDL_UnionFRect SDL_UnionFRect_renamed_SDL_GetRectUnionFloat
#define SDL_UnionRect SDL_UnionRect_renamed_SDL_GetRectUnion

/* ##SDL_render.h */
#define SDL_GetRendererOutputSize SDL_GetRendererOutputSize_renamed_SDL_GetCurrentRenderOutputSize
#define SDL_RenderCopy SDL_RenderCopy_renamed_SDL_RenderTexture
#define SDL_RenderCopyEx SDL_RenderCopyEx_renamed_SDL_RenderTextureRotated
#define SDL_RenderCopyExF SDL_RenderCopyExF_renamed_SDL_RenderTextureRotated
#define SDL_RenderCopyF SDL_RenderCopyF_renamed_SDL_RenderTexture
#define SDL_RenderDrawLine SDL_RenderDrawLine_renamed_SDL_RenderLine
#define SDL_RenderDrawLineF SDL_RenderDrawLineF_renamed_SDL_RenderLine
#define SDL_RenderDrawLines SDL_RenderDrawLines_renamed_SDL_RenderLines
#define SDL_RenderDrawLinesF SDL_RenderDrawLinesF_renamed_SDL_RenderLines
#define SDL_RenderDrawPoint SDL_RenderDrawPoint_renamed_SDL_RenderPoint
#define SDL_RenderDrawPointF SDL_RenderDrawPointF_renamed_SDL_RenderPoint
#define SDL_RenderDrawPoints SDL_RenderDrawPoints_renamed_SDL_RenderPoints
#define SDL_RenderDrawPointsF SDL_RenderDrawPointsF_renamed_SDL_RenderPoints
#define SDL_RenderDrawRect SDL_RenderDrawRect_renamed_SDL_RenderRect
#define SDL_RenderDrawRectF SDL_RenderDrawRectF_renamed_SDL_RenderRect
#define SDL_RenderDrawRects SDL_RenderDrawRects_renamed_SDL_RenderRects
#define SDL_RenderDrawRectsF SDL_RenderDrawRectsF_renamed_SDL_RenderRects
#define SDL_RenderFillRectF SDL_RenderFillRectF_renamed_SDL_RenderFillRect
#define SDL_RenderFillRectsF SDL_RenderFillRectsF_renamed_SDL_RenderFillRects
#define SDL_RendererFlip SDL_RendererFlip_renamed_SDL_FlipMode
#define SDL_RenderFlush SDL_RenderFlush_renamed_SDL_FlushRenderer
#define SDL_RenderGetClipRect SDL_RenderGetClipRect_renamed_SDL_GetRenderClipRect
#define SDL_RenderGetLogicalSize SDL_RenderGetLogicalSize_renamed_SDL_GetRenderLogicalPresentation
#define SDL_RenderGetMetalCommandEncoder SDL_RenderGetMetalCommandEncoder_renamed_SDL_GetRenderMetalCommandEncoder
#define SDL_RenderGetMetalLayer SDL_RenderGetMetalLayer_renamed_SDL_GetRenderMetalLayer
#define SDL_RenderGetScale SDL_RenderGetScale_renamed_SDL_GetRenderScale
#define SDL_RenderGetViewport SDL_RenderGetViewport_renamed_SDL_GetRenderViewport
#define SDL_RenderGetWindow SDL_RenderGetWindow_renamed_SDL_GetRenderWindow
#define SDL_RenderIsClipEnabled SDL_RenderIsClipEnabled_renamed_SDL_RenderClipEnabled
#define SDL_RenderLogicalToWindow SDL_RenderLogicalToWindow_renamed_SDL_RenderCoordinatesToWindow
#define SDL_RenderSetClipRect SDL_RenderSetClipRect_renamed_SDL_SetRenderClipRect
#define SDL_RenderSetLogicalSize SDL_RenderSetLogicalSize_renamed_SDL_SetRenderLogicalPresentation
#define SDL_RenderSetScale SDL_RenderSetScale_renamed_SDL_SetRenderScale
#define SDL_RenderSetVSync SDL_RenderSetVSync_renamed_SDL_SetRenderVSync
#define SDL_RenderSetViewport SDL_RenderSetViewport_renamed_SDL_SetRenderViewport
#define SDL_RenderWindowToLogical SDL_RenderWindowToLogical_renamed_SDL_RenderCoordinatesFromWindow
#define SDL_ScaleModeLinear SDL_ScaleModeLinear_renamed_SDL_SCALEMODE_LINEAR
#define SDL_ScaleModeNearest SDL_ScaleModeNearest_renamed_SDL_SCALEMODE_NEAREST

/* ##SDL_rwops.h */
#define RW_SEEK_CUR RW_SEEK_CUR_renamed_SDL_IO_SEEK_CUR
#define RW_SEEK_END RW_SEEK_END_renamed_SDL_IO_SEEK_END
#define RW_SEEK_SET RW_SEEK_SET_renamed_SDL_IO_SEEK_SET
#define SDL_RWFromConstMem SDL_RWFromConstMem_renamed_SDL_IOFromConstMem
#define SDL_RWFromFile SDL_RWFromFile_renamed_SDL_IOFromFile
#define SDL_RWFromMem SDL_RWFromMem_renamed_SDL_IOFromMem
#define SDL_RWclose SDL_RWclose_renamed_SDL_CloseIO
#define SDL_RWops SDL_RWops_renamed_SDL_IOStream
#define SDL_RWread SDL_RWread_renamed_SDL_ReadIO
#define SDL_RWseek SDL_RWseek_renamed_SDL_SeekIO
#define SDL_RWsize SDL_RWsize_renamed_SDL_GetIOSize
#define SDL_RWtell SDL_RWtell_renamed_SDL_TellIO
#define SDL_RWwrite SDL_RWwrite_renamed_SDL_WriteIO
#define SDL_ReadBE16 SDL_ReadBE16_renamed_SDL_ReadU16BE
#define SDL_ReadBE32 SDL_ReadBE32_renamed_SDL_ReadU32BE
#define SDL_ReadBE64 SDL_ReadBE64_renamed_SDL_ReadU64BE
#define SDL_ReadLE16 SDL_ReadLE16_renamed_SDL_ReadU16LE
#define SDL_ReadLE32 SDL_ReadLE32_renamed_SDL_ReadU32LE
#define SDL_ReadLE64 SDL_ReadLE64_renamed_SDL_ReadU64LE
#define SDL_WriteBE16 SDL_WriteBE16_renamed_SDL_WriteU16BE
#define SDL_WriteBE32 SDL_WriteBE32_renamed_SDL_WriteU32BE
#define SDL_WriteBE64 SDL_WriteBE64_renamed_SDL_WriteU64BE
#define SDL_WriteLE16 SDL_WriteLE16_renamed_SDL_WriteU16LE
#define SDL_WriteLE32 SDL_WriteLE32_renamed_SDL_WriteU32LE
#define SDL_WriteLE64 SDL_WriteLE64_renamed_SDL_WriteU64LE

/* ##SDL_scancode.h */
#define SDL_NUM_SCANCODES SDL_NUM_SCANCODES_renamed_SDL_SCANCODE_COUNT
#define SDL_SCANCODE_AUDIOFASTFORWARD SDL_SCANCODE_AUDIOFASTFORWARD_renamed_SDL_SCANCODE_MEDIA_FAST_FORWARD
#define SDL_SCANCODE_AUDIOMUTE SDL_SCANCODE_AUDIOMUTE_renamed_SDL_SCANCODE_MUTE
#define SDL_SCANCODE_AUDIONEXT SDL_SCANCODE_AUDIONEXT_renamed_SDL_SCANCODE_MEDIA_NEXT_TRACK
#define SDL_SCANCODE_AUDIOPLAY SDL_SCANCODE_AUDIOPLAY_renamed_SDL_SCANCODE_MEDIA_PLAY
#define SDL_SCANCODE_AUDIOPREV SDL_SCANCODE_AUDIOPREV_renamed_SDL_SCANCODE_MEDIA_PREVIOUS_TRACK
#define SDL_SCANCODE_AUDIOREWIND SDL_SCANCODE_AUDIOREWIND_renamed_SDL_SCANCODE_MEDIA_REWIND
#define SDL_SCANCODE_AUDIOSTOP SDL_SCANCODE_AUDIOSTOP_renamed_SDL_SCANCODE_MEDIA_STOP
#define SDL_SCANCODE_EJECT SDL_SCANCODE_EJECT_renamed_SDL_SCANCODE_MEDIA_EJECT
#define SDL_SCANCODE_MEDIASELECT SDL_SCANCODE_MEDIASELECT_renamed_SDL_SCANCODE_MEDIA_SELECT

/* ##SDL_sensor.h */
#define SDL_SensorClose SDL_SensorClose_renamed_SDL_CloseSensor
#define SDL_SensorFromInstanceID SDL_SensorFromInstanceID_renamed_SDL_GetSensorFromID
#define SDL_SensorGetData SDL_SensorGetData_renamed_SDL_GetSensorData
#define SDL_SensorGetInstanceID SDL_SensorGetInstanceID_renamed_SDL_GetSensorID
#define SDL_SensorGetName SDL_SensorGetName_renamed_SDL_GetSensorName
#define SDL_SensorGetNonPortableType SDL_SensorGetNonPortableType_renamed_SDL_GetSensorNonPortableType
#define SDL_SensorGetType SDL_SensorGetType_renamed_SDL_GetSensorType
#define SDL_SensorOpen SDL_SensorOpen_renamed_SDL_OpenSensor
#define SDL_SensorUpdate SDL_SensorUpdate_renamed_SDL_UpdateSensors

/* ##SDL_stdinc.h */
#define SDL_FALSE SDL_FALSE_renamed_false
#define SDL_TABLESIZE SDL_TABLESIZE_renamed_SDL_arraysize
#define SDL_TRUE SDL_TRUE_renamed_true
#define SDL_bool SDL_bool_renamed_bool
#define SDL_size_add_overflow SDL_size_add_overflow_renamed_SDL_size_add_check_overflow
#define SDL_size_mul_overflow SDL_size_mul_overflow_renamed_SDL_size_mul_check_overflow
#define SDL_strtokr SDL_strtokr_renamed_SDL_strtok_r

/* ##SDL_surface.h */
#define SDL_BlitScaled SDL_BlitScaled_renamed_SDL_BlitSurfaceScaled
#define SDL_ConvertSurfaceFormat SDL_ConvertSurfaceFormat_renamed_SDL_ConvertSurface
#define SDL_FillRect SDL_FillRect_renamed_SDL_FillSurfaceRect
#define SDL_FillRects SDL_FillRects_renamed_SDL_FillSurfaceRects
#define SDL_FreeSurface SDL_FreeSurface_renamed_SDL_DestroySurface
#define SDL_GetClipRect SDL_GetClipRect_renamed_SDL_GetSurfaceClipRect
#define SDL_GetColorKey SDL_GetColorKey_renamed_SDL_GetSurfaceColorKey
#define SDL_HasColorKey SDL_HasColorKey_renamed_SDL_SurfaceHasColorKey
#define SDL_HasSurfaceRLE SDL_HasSurfaceRLE_renamed_SDL_SurfaceHasRLE
#define SDL_LoadBMP_RW SDL_LoadBMP_RW_renamed_SDL_LoadBMP_IO
#define SDL_LowerBlit SDL_LowerBlit_renamed_SDL_BlitSurfaceUnchecked
#define SDL_LowerBlitScaled SDL_LowerBlitScaled_renamed_SDL_BlitSurfaceUncheckedScaled
#define SDL_PREALLOC SDL_PREALLOC_renamed_SDL_SURFACE_PREALLOCATED
#define SDL_SIMD_ALIGNED SDL_SIMD_ALIGNED_renamed_SDL_SURFACE_SIMD_ALIGNED
#define SDL_SaveBMP_RW SDL_SaveBMP_RW_renamed_SDL_SaveBMP_IO
#define SDL_SetClipRect SDL_SetClipRect_renamed_SDL_SetSurfaceClipRect
#define SDL_SetColorKey SDL_SetColorKey_renamed_SDL_SetSurfaceColorKey
#define SDL_UpperBlit SDL_UpperBlit_renamed_SDL_BlitSurface
#define SDL_UpperBlitScaled SDL_UpperBlitScaled_renamed_SDL_BlitSurfaceScaled

/* ##SDL_system.h */
#define SDL_AndroidBackButton SDL_AndroidBackButton_renamed_SDL_SendAndroidBackButton
#define SDL_AndroidGetActivity SDL_AndroidGetActivity_renamed_SDL_GetAndroidActivity
#define SDL_AndroidGetExternalStoragePath SDL_AndroidGetExternalStoragePath_renamed_SDL_GetAndroidExternalStoragePath
#define SDL_AndroidGetExternalStorageState SDL_AndroidGetExternalStorageState_renamed_SDL_GetAndroidExternalStorageState
#define SDL_AndroidGetInternalStoragePath SDL_AndroidGetInternalStoragePath_renamed_SDL_GetAndroidInternalStoragePath
#define SDL_AndroidGetJNIEnv SDL_AndroidGetJNIEnv_renamed_SDL_GetAndroidJNIEnv
#define SDL_AndroidRequestPermission SDL_AndroidRequestPermission_renamed_SDL_RequestAndroidPermission
#define SDL_AndroidRequestPermissionCallback SDL_AndroidRequestPermissionCallback_renamed_SDL_RequestAndroidPermissionCallback
#define SDL_AndroidSendMessage SDL_AndroidSendMessage_renamed_SDL_SendAndroidMessage
#define SDL_AndroidShowToast SDL_AndroidShowToast_renamed_SDL_ShowAndroidToast
#define SDL_DXGIGetOutputInfo SDL_DXGIGetOutputInfo_renamed_SDL_GetDXGIOutputInfo
#define SDL_Direct3D9GetAdapterIndex SDL_Direct3D9GetAdapterIndex_renamed_SDL_GetDirect3D9AdapterIndex
#define SDL_GDKGetDefaultUser SDL_GDKGetDefaultUser_renamed_SDL_GetGDKDefaultUser
#define SDL_GDKGetTaskQueue SDL_GDKGetTaskQueue_renamed_SDL_GetGDKTaskQueue
#define SDL_LinuxSetThreadPriority SDL_LinuxSetThreadPriority_renamed_SDL_SetLinuxThreadPriority
#define SDL_LinuxSetThreadPriorityAndPolicy SDL_LinuxSetThreadPriorityAndPolicy_renamed_SDL_SetLinuxThreadPriorityAndPolicy
#define SDL_OnApplicationDidBecomeActive SDL_OnApplicationDidBecomeActive_renamed_SDL_OnApplicationDidEnterForeground
#define SDL_OnApplicationWillResignActive SDL_OnApplicationWillResignActive_renamed_SDL_OnApplicationWillEnterBackground
#define SDL_iOSSetAnimationCallback SDL_iOSSetAnimationCallback_renamed_SDL_SetiOSAnimationCallback
#define SDL_iOSSetEventPump SDL_iOSSetEventPump_renamed_SDL_SetiOSEventPump
#define SDL_iPhoneSetAnimationCallback SDL_iPhoneSetAnimationCallback_renamed_SDL_iOSSetAnimationCallback
#define SDL_iPhoneSetEventPump SDL_iPhoneSetEventPump_renamed_SDL_iOSSetEventPump

/* ##SDL_thread.h */
#define SDL_SetThreadPriority SDL_SetThreadPriority_renamed_SDL_SetCurrentThreadPriority
#define SDL_TLSCleanup SDL_TLSCleanup_renamed_SDL_CleanupTLS
#define SDL_TLSGet SDL_TLSGet_renamed_SDL_GetTLS
#define SDL_TLSSet SDL_TLSSet_renamed_SDL_SetTLS
#define SDL_threadID SDL_threadID_renamed_SDL_ThreadID

/* ##SDL_timer.h */
#define SDL_GetTicks64 SDL_GetTicks64_renamed_SDL_GetTicks

/* ##SDL_version.h */
#define SDL_COMPILEDVERSION SDL_COMPILEDVERSION_renamed_SDL_VERSION
#define SDL_PATCHLEVEL SDL_PATCHLEVEL_renamed_SDL_MICRO_VERSION

/* ##SDL_video.h */
#define SDL_GL_DeleteContext SDL_GL_DeleteContext_renamed_SDL_GL_DestroyContext
#define SDL_GLattr SDL_GLattr_renamed_SDL_GLAttr
#define SDL_GLcontextFlag SDL_GLcontextFlag_renamed_SDL_GLContextFlag
#define SDL_GLcontextReleaseFlag SDL_GLcontextReleaseFlag_renamed_SDL_GLContextReleaseFlag
#define SDL_GLprofile SDL_GLprofile_renamed_SDL_GLProfile
#define SDL_GetClosestDisplayMode SDL_GetClosestDisplayMode_renamed_SDL_GetClosestFullscreenDisplayMode
#define SDL_GetDisplayOrientation SDL_GetDisplayOrientation_renamed_SDL_GetCurrentDisplayOrientation
#define SDL_GetPointDisplayIndex SDL_GetPointDisplayIndex_renamed_SDL_GetDisplayForPoint
#define SDL_GetRectDisplayIndex SDL_GetRectDisplayIndex_renamed_SDL_GetDisplayForRect
#define SDL_GetWindowDisplayIndex SDL_GetWindowDisplayIndex_renamed_SDL_GetDisplayForWindow
#define SDL_GetWindowDisplayMode SDL_GetWindowDisplayMode_renamed_SDL_GetWindowFullscreenMode
#define SDL_HasWindowSurface SDL_HasWindowSurface_renamed_SDL_WindowHasSurface
#define SDL_IsScreenSaverEnabled SDL_IsScreenSaverEnabled_renamed_SDL_ScreenSaverEnabled
#define SDL_SetWindowDisplayMode SDL_SetWindowDisplayMode_renamed_SDL_SetWindowFullscreenMode
#define SDL_WINDOW_ALLOW_HIGHDPI SDL_WINDOW_ALLOW_HIGHDPI_renamed_SDL_WINDOW_HIGH_PIXEL_DENSITY
#define SDL_WINDOW_INPUT_GRABBED SDL_WINDOW_INPUT_GRABBED_renamed_SDL_WINDOW_MOUSE_GRABBED
#define SDL_WINDOW_SKIP_TASKBAR SDL_WINDOW_SKIP_TASKBAR_renamed_SDL_WINDOW_UTILITY

#endif /* SDL_ENABLE_OLD_NAMES */

#endif /* SDL_oldnames_h_ */
