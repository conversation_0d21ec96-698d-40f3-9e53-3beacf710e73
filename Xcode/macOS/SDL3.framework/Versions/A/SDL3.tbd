{"main_library": {"compatibility_versions": [{"version": "201"}], "current_versions": [{"version": "201"}], "exported_symbols": [{"text": {"global": ["_SDL_AcquireCameraFrame", "_SDL_AcquireGPUCommandBuffer", "_SDL_AcquireGPUSwapchainTexture", "_SDL_AddAtomicInt", "_SDL_AddEventWatch", "_SDL_AddGamepadMapping", "_SDL_AddGamepadMappingsFromFile", "_SDL_AddGamepadMappingsFromIO", "_SDL_AddHintCallback", "_SDL_AddSurfaceAlternateImage", "_SDL_AddTimer", "_SDL_AddTimerNS", "_SDL_AddVulkanRenderSemaphores", "_SDL_AttachVirtualJoystick", "_SDL_AudioDevicePaused", "_SDL_BeginGPUComputePass", "_SDL_BeginGPUCopyPass", "_SDL_BeginGPURenderPass", "_SDL_BindAudioStream", "_SDL_BindAudioStreams", "_SDL_BindGPUComputePipeline", "_SDL_BindGPUComputeSamplers", "_SDL_BindGPUComputeStorageBuffers", "_SDL_BindGPUComputeStorageTextures", "_SDL_BindGPUFragmentSamplers", "_SDL_BindGPUFragmentStorageBuffers", "_SDL_BindGPUFragmentStorageTextures", "_SDL_BindGPUGraphicsPipeline", "_SDL_BindGPUIndexBuffer", "_SDL_BindGPUVertexBuffers", "_SDL_BindGPUVertexSamplers", "_SDL_BindGPUVertexStorageBuffers", "_SDL_BindGPUVertexStorageTextures", "_SDL_BlitGPUTexture", "_SDL_BlitSurface9Grid", "_SDL_BlitSurface", "_SDL_BlitSurfaceScaled", "_SDL_BlitSurfaceTiled", "_SDL_BlitSurfaceTiledWithScale", "_SDL_BlitSurfaceUnchecked", "_SDL_BlitSurfaceUncheckedScaled", "_SDL_BroadcastCondition", "_SDL_CaptureMouse", "_SDL_ClaimWindowForGPUDevice", "_SDL_CleanupTLS", "_SDL_ClearAudioStream", "_SDL_ClearClipboardData", "_SDL_ClearComposition", "_SDL_ClearError", "_SDL_ClearProperty", "_SDL_ClearSurface", "_SDL_CloseAudioDevice", "_SDL_CloseCamera", "_SDL_CloseGamepad", "_SDL_CloseHaptic", "_SDL_CloseIO", "_SDL_<PERSON><PERSON><PERSON><PERSON>", "_SDL_CloseSensor", "_SDL_CloseStorage", "_SDL_CompareAndSwapAtomicInt", "_SDL_CompareAndSwapAtomicPointer", "_SDL_CompareAndSwapAtomicU32", "_SDL_ComposeCustomBlendMode", "_SDL_ConvertAudioSamples", "_SDL_ConvertEventToRenderCoordinates", "_SDL_ConvertPixels", "_SDL_ConvertPixelsAndColorspace", "_SDL_ConvertSurface", "_SDL_ConvertSurfaceAndColorspace", "_SDL_CopyFile", "_SDL_CopyGPUBufferToBuffer", "_SDL_CopyGPUTextureToTexture", "_SDL_CopyProperties", "_SDL_CopyStorageFile", "_SDL_CreateAudioStream", "_SDL_CreateColorCursor", "_SDL_CreateCondition", "_SDL_CreateCursor", "_SDL_CreateDirectory", "_SDL_CreateEnvironment", "_SDL_CreateGPUBuffer", "_SDL_CreateGPUComputePipeline", "_SDL_CreateGPUDevice", "_SDL_CreateGPUDeviceWithProperties", "_SDL_CreateGPUGraphicsPipeline", "_SDL_CreateGPUSampler", "_SDL_CreateGPUShader", "_SDL_CreateGPUTexture", "_SDL_CreateGPUTransferBuffer", "_SDL_CreateHapticEffect", "_SDL_CreateMutex", "_SDL_CreatePalette", "_SDL_CreatePopupWindow", "_SDL_CreateProcess", "_SDL_CreateProcessWithProperties", "_SDL_CreateProperties", "_SDL_CreateRWLock", "_SDL_C<PERSON><PERSON><PERSON><PERSON>", "_SDL_CreateRendererWithProperties", "_SDL_CreateSemaphore", "_SDL_CreateSoftwareRenderer", "_SDL_CreateStorageDirectory", "_SDL_CreateSurface", "_SDL_CreateSurfaceFrom", "_SDL_CreateSurfacePalette", "_SDL_CreateSystemCursor", "_SDL_CreateTexture", "_SDL_CreateTextureFromSurface", "_SDL_CreateTextureWithProperties", "_SDL_CreateThreadRuntime", "_SDL_CreateThreadWithPropertiesRuntime", "_SDL_CreateWindow", "_SDL_CreateWindowAndRenderer", "_SDL_CreateWindowWithProperties", "_SDL_CursorVisible", "_SDL_DateTimeToTime", "_SDL_Delay", "_SDL_DelayNS", "_SDL_DestroyAudioStream", "_SDL_DestroyCondition", "_SDL_DestroyCursor", "_SDL_DestroyEnvironment", "_SDL_DestroyGPUDevice", "_SDL_DestroyHapticEffect", "_SDL_DestroyMutex", "_SDL_DestroyPalette", "_SDL_DestroyProcess", "_SDL_DestroyProperties", "_SDL_DestroyRWLock", "_SDL_DestroyR<PERSON>er", "_SDL_DestroySemaphore", "_SDL_DestroySurface", "_SDL_DestroyTexture", "_SDL_DestroyWindow", "_SDL_DestroyWindowSurface", "_SDL_DetachThread", "_SDL_DetachVirtualJoystick", "_SDL_DisableScreenSaver", "_SDL_DispatchGPUCompute", "_SDL_DispatchGPUComputeIndirect", "_SDL_DownloadFromGPUBuffer", "_SDL_DownloadFromGPUTexture", "_SDL_DrawGPUIndexedPrimitives", "_SDL_DrawGPUIndexedPrimitivesIndirect", "_SDL_DrawGPUPrimitives", "_SDL_DrawGPUPrimitivesIndirect", "_SDL_DuplicateSurface", "_SDL_EGL_GetCurrentConfig", "_SDL_EGL_GetCurrentDisplay", "_SDL_EGL_GetProcAddress", "_SDL_EGL_GetWindowSurface", "_SDL_EGL_SetAttributeCallbacks", "_SDL_EnableScreenSaver", "_SDL_EndGPUComputePass", "_SDL_EndGPUCopyPass", "_SDL_EndGPURenderPass", "_SDL_EnterAppMainCallbacks", "_SDL_EnumerateDirectory", "_SDL_EnumerateProperties", "_SDL_EnumerateStorageDirectory", "_SDL_EventEnabled", "_SDL_FillSurfaceRect", "_SDL_FillSurfaceRects", "_SDL_FilterEvents", "_SDL_FlashWindow", "_SDL_FlipSurface", "_SDL_FlushAudioStream", "_SDL_FlushEvent", "_SDL_FlushEvents", "_SDL_FlushIO", "_SDL_Flush<PERSON><PERSON>er", "_SDL_GDKResumeGPU", "_SDL_GDKSuspendComplete", "_SDL_GDKSuspendGPU", "_SDL_GL_CreateContext", "_SDL_GL_DestroyContext", "_SDL_GL_ExtensionSupported", "_SDL_GL_GetAttribute", "_SDL_GL_GetCurrentContext", "_SDL_GL_GetCurrentWindow", "_SDL_GL_GetProcAddress", "_SDL_GL_GetSwapInterval", "_SDL_GL_LoadLibrary", "_SDL_GL_MakeCurrent", "_SDL_GL_ResetAttributes", "_SDL_GL_SetAttribute", "_SDL_GL_SetSwapInterval", "_SDL_GL_SwapWindow", "_SDL_GL_UnloadLibrary", "_SDL_GPUSupportsProperties", "_SDL_GPUSupportsShaderFormats", "_SDL_GPUTextureFormatTexelBlockSize", "_SDL_GPUTextureSupportsFormat", "_SDL_GPUTextureSupportsSampleCount", "_SDL_GUIDToString", "_SDL_GamepadConnected", "_SDL_GamepadEventsEnabled", "_SDL_GamepadHasAxis", "_SDL_GamepadHasButton", "_SDL_GamepadHasSensor", "_SDL_GamepadSensorEnabled", "_SDL_GenerateMipmapsForGPUTexture", "_SDL_GetAndroidActivity", "_SDL_GetAndroidCachePath", "_SDL_GetAndroidExternalStoragePath", "_SDL_GetAndroidExternalStorageState", "_SDL_GetAndroidInternalStoragePath", "_SDL_GetAndroidJNIEnv", "_SDL_GetAndroidSDKVersion", "_SDL_GetAppMetadataProperty", "_SDL_GetAssertionHandler", "_SDL_GetAssertionReport", "_SDL_GetAtomicInt", "_SDL_GetAtomicPointer", "_SDL_GetAtomicU32", "_SDL_GetAudioDeviceChannelMap", "_SDL_GetAudioDeviceFormat", "_SDL_GetAudioDeviceGain", "_SDL_GetAudioDeviceName", "_SDL_GetAudioDriver", "_SDL_GetAudioFormatName", "_SDL_GetAudioPlaybackDevices", "_SDL_GetAudioRecordingDevices", "_SDL_GetAudioStreamAvailable", "_SDL_GetAudioStreamData", "_SDL_GetAudioStreamDevice", "_SDL_GetAudioStreamFormat", "_SDL_GetAudioStreamFrequencyRatio", "_SDL_GetAudioStreamGain", "_SDL_GetAudioStreamInputChannelMap", "_SDL_GetAudioStreamOutputChannelMap", "_SDL_GetAudioStreamProperties", "_SDL_GetAudioStreamQueued", "_SDL_GetBasePath", "_SDL_GetBooleanProperty", "_SDL_GetCPUCacheLineSize", "_SDL_GetCameraDriver", "_SDL_GetCameraFormat", "_SDL_GetCameraID", "_SDL_GetCameraName", "_SDL_GetCameraPermissionState", "_SDL_GetCameraPosition", "_SDL_GetCameraProperties", "_SDL_GetCameraSupportedFormats", "_SDL_GetCameras", "_SDL_GetClipboardData", "_SDL_GetClipboardMimeTypes", "_SDL_GetClipboardText", "_SDL_GetClosestFullscreenDisplayMode", "_SDL_GetCurrentAudioDriver", "_SDL_GetCurrentCameraDriver", "_SDL_GetCurrentDisplayMode", "_SDL_GetCurrentDisplayOrientation", "_SDL_GetCurrentRenderOutputSize", "_SDL_GetCurrentThreadID", "_SDL_GetCurrentTime", "_SDL_GetCurrentVideoDriver", "_SDL_GetCursor", "_SDL_GetDXGIOutputInfo", "_SDL_GetDateTimeLocalePreferences", "_SDL_GetDayOfWeek", "_SDL_GetDayOfYear", "_SDL_GetDaysInMonth", "_SDL_GetDefaultAssertionHandler", "_SDL_GetDefaultCursor", "_SDL_GetDesktopDisplayMode", "_SDL_GetDirect3D9AdapterIndex", "_SDL_GetDisplayBounds", "_SDL_GetDisplayContentScale", "_SDL_GetDisplayForPoint", "_SDL_GetDisplayForRect", "_SDL_GetDisplayForWindow", "_SDL_GetDisplayName", "_SDL_GetDisplayProperties", "_SDL_GetDisplayUsableBounds", "_SDL_GetDisplays", "_SDL_GetEnvironment", "_SDL_GetEnvironmentVariable", "_SDL_GetEnvironmentVariables", "_SDL_GetError", "_SDL_GetEventFilter", "_SDL_GetFloatProperty", "_SDL_GetFullscreenDisplayModes", "_SDL_GetGDKDefaultUser", "_SDL_GetGDKTaskQueue", "_SDL_GetGPUDeviceDriver", "_SDL_GetGPUDriver", "_SDL_GetGPUShaderFormats", "_SDL_GetGPUSwapchainTextureFormat", "_SDL_GetGamepadAppleSFSymbolsNameForAxis", "_SDL_GetGamepadAppleSFSymbolsNameForButton", "_SDL_GetGamepadAxis", "_SDL_GetGamepadAxisFromString", "_SDL_GetGamepadBindings", "_SDL_GetGamepadButton", "_SDL_GetGamepadButtonFromString", "_SDL_GetGamepadButtonLabel", "_SDL_GetGamepadButtonLabelForType", "_SDL_GetGamepadConnectionState", "_SDL_GetGamepadFirmwareVersion", "_SDL_GetGamepadFromID", "_SDL_GetGamepadFromPlayerIndex", "_SDL_GetGamepadGUIDForID", "_SDL_GetGamepadID", "_SDL_GetGamepadJoystick", "_SDL_GetGamepadMapping", "_SDL_GetGamepadMappingForGUID", "_SDL_GetGamepadMappingForID", "_SDL_GetGamepadMappings", "_SDL_GetGamepadName", "_SDL_GetGamepadNameForID", "_SDL_GetGamepadPath", "_SDL_GetGamepadPathForID", "_SDL_GetGamepadPlayerIndex", "_SDL_GetGamepadPlayerIndexForID", "_SDL_GetGamepadPowerInfo", "_SDL_GetGamepadProduct", "_SDL_GetGamepadProductForID", "_SDL_GetGamepadProductVersion", "_SDL_GetGamepadProductVersionForID", "_SDL_GetGamepadProperties", "_SDL_GetGamepadSensorData", "_SDL_GetGamepadSensorDataRate", "_SDL_GetGamepadSerial", "_SDL_GetGamepadSteamHandle", "_SDL_GetGamepadStringForAxis", "_SDL_GetGamepadStringForButton", "_SDL_GetGamepadStringForType", "_SDL_GetGamepadTouchpadFinger", "_SDL_GetGamepadType", "_SDL_GetGamepadTypeForID", "_SDL_GetGamepadTypeFromString", "_SDL_GetGamepadVendor", "_SDL_GetGamepadVendorForID", "_SDL_GetGamepads", "_SDL_GetGlobalMouseState", "_SDL_GetGlobalProperties", "_SDL_GetGrabbedWindow", "_SDL_GetHapticEffectStatus", "_SDL_GetHapticFeatures", "_SDL_GetHapticFromID", "_SDL_GetHapticID", "_SDL_GetHapticName", "_SDL_GetHapticNameForID", "_SDL_GetHaptics", "_SDL_GetHint", "_SDL_GetHintBoolean", "_SDL_GetIOProperties", "_SDL_GetIOSize", "_SDL_GetIOStatus", "_SDL_GetJoystickAxis", "_SDL_GetJoystickAxisInitialState", "_SDL_<PERSON><PERSON><PERSON>stickBall", "_SDL_Get<PERSON><PERSON>stick<PERSON>utton", "_SDL_GetJoystickConnectionState", "_SDL_GetJoystickFirmwareVersion", "_SDL_GetJoystickFromID", "_SDL_GetJoystickFromPlayerIndex", "_SDL_GetJoystickGUID", "_SDL_GetJoystickGUIDForID", "_SDL_GetJoystickGUIDInfo", "_SDL_GetJoystickHat", "_SDL_GetJoystickID", "_SDL_Get<PERSON><PERSON>stick<PERSON>ame", "_SDL_GetJoystickNameForID", "_SDL_Get<PERSON>oystickPath", "_SDL_GetJoystickPathForID", "_SDL_GetJoystickPlayerIndex", "_SDL_GetJoystickPlayerIndexForID", "_SDL_GetJoystickPowerInfo", "_SDL_GetJoystickProduct", "_SDL_GetJoystickProductForID", "_SDL_GetJoystickProductVersion", "_SDL_GetJoystickProductVersionForID", "_SDL_GetJoystickProperties", "_SDL_GetJoystickSerial", "_SDL_GetJoystickType", "_SDL_GetJoystickTypeForID", "_SDL_GetJoystickVendor", "_SDL_GetJoystickVendorForID", "_SDL_GetJoysticks", "_SDL_GetKeyFromName", "_SDL_GetKeyFromScancode", "_SDL_GetKeyName", "_SDL_GetKeyboardFocus", "_SDL_GetKeyboardNameForID", "_SDL_GetKeyboardState", "_SDL_GetKeyboards", "_SDL_GetLogOutputFunction", "_SDL_GetLogPriority", "_SDL_GetMasksForPixelFormat", "_SDL_GetMaxHapticEffects", "_SDL_GetMaxHapticEffectsPlaying", "_SDL_GetMemoryFunctions", "_SDL_GetMice", "_SDL_GetModState", "_SDL_GetMouseFocus", "_SDL_GetMouseNameForID", "_SDL_GetMouseState", "_SDL_GetNaturalDisplayOrientation", "_SDL_GetNumAllocations", "_SDL_GetNumAudioDrivers", "_SDL_GetNumCameraDrivers", "_SDL_GetNumGPUDrivers", "_SDL_GetNumGamepadTouchpadFingers", "_SDL_GetNumGamepadTouchpads", "_SDL_GetNumHapticAxes", "_SDL_GetNumJoystickAxes", "_SDL_GetNumJoystickBalls", "_SDL_GetNumJoystickButtons", "_SDL_GetNumJoystickHats", "_SDL_GetNumLogicalCPUCores", "_SDL_GetNumRenderDrivers", "_SDL_GetNumVideoDrivers", "_SDL_GetNumberProperty", "_SDL_GetOriginalMemoryFunctions", "_SDL_GetPathInfo", "_SDL_GetPerformanceCounter", "_SDL_GetPerformanceFrequency", "_SDL_GetPixelFormatDetails", "_SDL_GetPixelFormatForMasks", "_SDL_GetPixelFormatName", "_SDL_GetPlatform", "_SDL_GetPointerProperty", "_SDL_GetPowerInfo", "_SDL_GetPrefPath", "_SDL_GetPreferredLocales", "_SDL_GetPrimaryDisplay", "_SDL_GetPrimarySelectionText", "_SDL_GetProcessInput", "_SDL_GetProcessOutput", "_SDL_GetProcessProperties", "_SDL_GetPropertyType", "_SDL_GetRGB", "_SDL_GetRGBA", "_SDL_GetRealGamepadType", "_SDL_GetRealGamepadTypeForID", "_SDL_GetRectAndLineIntersection", "_SDL_GetRectAndLineIntersectionFloat", "_SDL_GetRectEnclosingPoints", "_SDL_GetRectEnclosingPointsFloat", "_SDL_GetRectIntersection", "_SDL_GetRectIntersectionFloat", "_SDL_GetRectUnion", "_SDL_GetRectUnionFloat", "_SDL_GetRelativeMouseState", "_SDL_GetRenderClipRect", "_SDL_GetRenderColorScale", "_SDL_GetRenderDrawBlendMode", "_SDL_GetRenderDrawColor", "_SDL_GetRenderDrawColorFloat", "_SDL_GetRenderDriver", "_SDL_GetRenderLogicalPresentation", "_SDL_GetRenderLogicalPresentationRect", "_SDL_GetRenderMetalCommandEncoder", "_SDL_GetRenderMetalLayer", "_SDL_GetRenderOutputSize", "_SDL_GetRenderSafeArea", "_SDL_GetRenderScale", "_SDL_GetRenderTarget", "_SDL_GetRenderVSync", "_SDL_GetRenderViewport", "_SDL_GetRenderWindow", "_SDL_<PERSON><PERSON><PERSON><PERSON>", "_SDL_GetRendererFromTexture", "_SDL_GetRendererName", "_SDL_GetRendererProperties", "_SDL_GetRevision", "_SDL_GetSIMDAlignment", "_SDL_GetScancodeFromKey", "_SDL_GetScancodeFromName", "_SDL_GetScancodeName", "_SDL_GetSemaphoreValue", "_SDL_GetSensorData", "_SDL_GetSensorFromID", "_SDL_GetSensorID", "_SDL_GetSensorName", "_SDL_GetSensorNameForID", "_SDL_GetSensorNonPortableType", "_SDL_GetSensorNonPortableTypeForID", "_SDL_GetSensorProperties", "_SDL_GetSensorType", "_SDL_GetSensorTypeForID", "_SDL_GetSensors", "_SDL_GetSilenceValueForFormat", "_SDL_GetStorageFileSize", "_SDL_GetStoragePathInfo", "_SDL_GetStorageSpaceRemaining", "_SDL_GetStringProperty", "_SDL_GetSurfaceAlphaMod", "_SDL_GetSurfaceBlendMode", "_SDL_GetSurfaceClipRect", "_SDL_GetSurfaceColorKey", "_SDL_GetSurfaceColorMod", "_SDL_GetSurfaceColorspace", "_SDL_GetSurfaceImages", "_SDL_GetSurfacePalette", "_SDL_GetSurfaceProperties", "_SDL_GetSystemRAM", "_SDL_GetSystemTheme", "_SDL_GetTLS", "_SDL_GetTextInputArea", "_SDL_GetTextureAlphaMod", "_SDL_GetTextureAlphaModFloat", "_SDL_GetTextureBlendMode", "_SDL_GetTextureColorMod", "_SDL_GetTextureColorModFloat", "_SDL_GetTextureProperties", "_SDL_GetTextureScaleMode", "_SDL_GetTextureSize", "_SDL_GetThreadID", "_SDL_GetThreadName", "_SDL_GetTicks", "_SDL_GetTicksNS", "_SDL_GetTouchDeviceName", "_SDL_GetTouchDeviceType", "_SDL_GetTouchDevices", "_SDL_GetT<PERSON>ch<PERSON>ingers", "_SDL_GetUserFolder", "_SDL_GetVersion", "_SDL_GetVideoDriver", "_SDL_GetWindowAspectRatio", "_SDL_GetWindowBordersSize", "_SDL_GetWindowDisplayScale", "_SDL_GetWindowFlags", "_SDL_GetWindowFromEvent", "_SDL_GetWindowFromID", "_SDL_GetWindowFullscreenMode", "_SDL_GetWindowICCProfile", "_SDL_GetWindowID", "_SDL_GetWindowKeyboardGrab", "_SDL_GetWindowMaximumSize", "_SDL_GetWindowMinimumSize", "_SDL_GetWindowMouseGrab", "_SDL_GetWindowMouseRect", "_SDL_GetWindowOpacity", "_SDL_GetWindowParent", "_SDL_GetWindowPixelDensity", "_SDL_GetWindowPixelFormat", "_SDL_GetWindowPosition", "_SDL_GetWindowProperties", "_SDL_GetWindowRelativeMouseMode", "_SDL_GetWindowSafeArea", "_SDL_GetWindowSize", "_SDL_GetWindowSizeInPixels", "_SDL_GetWindowSurface", "_SDL_GetWindowSurfaceVSync", "_SDL_GetWindowTitle", "_SDL_GetWindows", "_SDL_GlobDirectory", "_SDL_GlobStorageDirectory", "_SDL_HapticEffectSupported", "_SDL_HapticRumbleSupported", "_SDL_HasARMSIMD", "_SDL_HasAVX2", "_SDL_HasAVX512F", "_SDL_HasAVX", "_SDL_HasAltiVec", "_SDL_HasClipboardData", "_SDL_HasClipboardText", "_SDL_HasEvent", "_SDL_HasEvents", "_SDL_HasGamepad", "_SDL_<PERSON><PERSON><PERSON>", "_SDL_HasKeyboard", "_SDL_HasLASX", "_SDL_HasLSX", "_SDL_HasMMX", "_SDL_HasMouse", "_SDL_HasNEON", "_SDL_HasPrimarySelectionText", "_SDL_HasProperty", "_SDL_HasRectIntersection", "_SDL_HasRectIntersectionFloat", "_SDL_HasSSE2", "_SDL_HasSSE3", "_SDL_HasSSE41", "_SDL_HasSSE42", "_SDL_HasSSE", "_SDL_HasScreenKeyboardSupport", "_SDL_HideCursor", "_SDL_HideWindow", "_SDL_IOFromConstMem", "_SDL_IOFromDynamicMem", "_SDL_IOFromFile", "_SDL_IOFromMem", "_SDL_IOprintf", "_SDL_IOvprintf", "_SDL_Init", "_SDL_InitHapticRumble", "_SDL_InitSubSystem", "_SDL_InsertGPUDebugLabel", "_SDL_IsChromebook", "_SDL_IsDeXMode", "_SDL_IsGamepad", "_SDL_Is<PERSON><PERSON>stick<PERSON>aptic", "_SDL_IsJoystickVirtual", "_SDL_IsMouseHaptic", "_SDL_IsTV", "_SDL_IsTablet", "_SDL_JoystickConnected", "_SDL_JoystickEventsEnabled", "_SDL_KillProcess", "_SDL_LoadBMP", "_SDL_LoadBMP_IO", "_SDL_LoadFile", "_SDL_LoadFile_IO", "_SDL_LoadFunction", "_SDL_LoadObject", "_SDL_LoadWAV", "_SDL_LoadWAV_IO", "_SDL_LockAudioStream", "_SDL_LockJoysticks", "_SDL_LockMutex", "_SDL_LockProperties", "_SDL_LockRWLockForReading", "_SDL_LockRWLockForWriting", "_SDL_LockSpinlock", "_SDL_LockSurface", "_SDL_LockTexture", "_SDL_LockTextureToSurface", "_SDL_Log", "_SDL_LogCritical", "_SDL_LogDebug", "_SDL_LogError", "_SDL_LogInfo", "_SDL_LogMessage", "_SDL_LogMessageV", "_SDL_LogTrace", "_SDL_LogVerbose", "_SDL_LogWarn", "_SDL_MapGPUTransferBuffer", "_SDL_MapRGB", "_SDL_MapRGBA", "_SDL_MapSurfaceRGB", "_SDL_MapSurfaceRGBA", "_SDL_MaximizeWindow", "_SDL_MemoryBarrierAcquireFunction", "_SDL_MemoryBarrierReleaseFunction", "_SDL_Metal_CreateView", "_SDL_Metal_DestroyView", "_SDL_Metal_GetLayer", "_SDL_MinimizeWindow", "_SDL_MixAudio", "_SDL_OnApplicationDidChangeStatusBarOrientation", "_SDL_OnApplicationDidEnterBackground", "_SDL_OnApplicationDidEnterForeground", "_SDL_OnApplicationDidReceiveMemoryWarning", "_SDL_OnApplicationWillEnterBackground", "_SDL_OnApplicationWillEnterForeground", "_SDL_OnApplicationWillTerminate", "_SDL_OpenAudioDevice", "_SDL_OpenAudioDeviceStream", "_SDL_OpenCamera", "_SDL_OpenFileStorage", "_SDL_OpenGamepad", "_SDL_OpenHaptic", "_SDL_OpenHapticFromJoystick", "_SDL_OpenHapticFromMouse", "_SDL_OpenIO", "_SDL_OpenJoystick", "_SDL_OpenSensor", "_SDL_OpenStorage", "_SDL_OpenTitleStorage", "_SDL_OpenURL", "_SDL_OpenUserStorage", "_SDL_OutOfMemory", "_SDL_PauseAudioDevice", "_SDL_PauseAudioStreamDevice", "_SDL_PauseHaptic", "_SDL_PeepEvents", "_SDL_PlayHapticRumble", "_SDL_PollEvent", "_SDL_PopGPUDebugGroup", "_SDL_PremultiplyAlpha", "_SDL_PremultiplySurfaceAlpha", "_SDL_PumpEvents", "_SDL_PushEvent", "_SDL_PushGPUComputeUniformData", "_SDL_PushGPUDebugGroup", "_SDL_PushGPUFragmentUniformData", "_SDL_PushGPUVertexUniformData", "_SDL_PutAudioStreamData", "_SDL_QueryGPUFence", "_SDL_Quit", "_SDL_QuitSubSystem", "_SDL_RaiseWindow", "_SDL_ReadIO", "_SDL_ReadProcess", "_SDL_ReadS16BE", "_SDL_ReadS16LE", "_SDL_ReadS32BE", "_SDL_ReadS32LE", "_SDL_ReadS64BE", "_SDL_ReadS64LE", "_SDL_ReadS8", "_SDL_ReadStorageFile", "_SDL_ReadSurfacePixel", "_SDL_ReadSurfacePixelFloat", "_SDL_ReadU16BE", "_SDL_ReadU16LE", "_SDL_ReadU32BE", "_SDL_ReadU32LE", "_SDL_ReadU64BE", "_SDL_ReadU64LE", "_SDL_ReadU8", "_SDL_RegisterApp", "_SDL_RegisterEvents", "_SDL_ReleaseCameraFrame", "_SDL_ReleaseGPUBuffer", "_SDL_ReleaseGPUComputePipeline", "_SDL_ReleaseGPUFence", "_SDL_ReleaseGPUGraphicsPipeline", "_SDL_ReleaseGPUSampler", "_SDL_ReleaseGPUShader", "_SDL_ReleaseGPUTexture", "_SDL_ReleaseGPUTransferBuffer", "_SDL_ReleaseWindowFromGPUDevice", "_SDL_ReloadGamepadMappings", "_SDL_RemoveEventWatch", "_SDL_RemoveHintCallback", "_SDL_RemovePath", "_SDL_RemoveStoragePath", "_SDL_RemoveSurfaceAlternateImages", "_SDL_RemoveTimer", "_SDL_RenamePath", "_SDL_RenameStoragePath", "_SDL_RenderClear", "_SDL_RenderClipEnabled", "_SDL_RenderCoordinatesFromWindow", "_SDL_RenderCoordinatesToWindow", "_SDL_RenderFillRect", "_SDL_RenderFillRects", "_SDL_RenderGeometry", "_SDL_RenderGeometryRaw", "_SDL_RenderLine", "_SDL_RenderLines", "_SDL_RenderPoint", "_SDL_RenderPoints", "_SDL_RenderPresent", "_SDL_RenderReadPixels", "_SDL_RenderRect", "_SDL_RenderRects", "_SDL_RenderTexture9Grid", "_SDL_RenderTexture", "_SDL_RenderTextureRotated", "_SDL_RenderTextureTiled", "_SDL_RenderViewportSet", "_SDL_ReportAssertion", "_SDL_RequestAndroidPermission", "_SDL_ResetAssertionReport", "_SDL_ResetHint", "_SDL_ResetHints", "_SDL_ResetKeyboard", "_SDL_ResetLogPriorities", "_SDL_RestoreWindow", "_SDL_ResumeAudioDevice", "_SDL_ResumeAudioStreamDevice", "_SDL_ResumeHaptic", "_SDL_RumbleGamepad", "_SDL_RumbleGamepadTriggers", "_SDL_RumbleJ<PERSON>stick", "_SDL_RumbleJoystickTriggers", "_SDL_RunApp", "_SDL_RunHapticEffect", "_SDL_SaveBMP", "_SDL_SaveBMP_IO", "_SDL_ScaleSurface", "_SDL_ScreenKeyboardShown", "_SDL_ScreenSaverEnabled", "_SDL_SeekIO", "_SDL_SendAndroidBackButton", "_SDL_SendAndroidMessage", "_SDL_SendGamepadEffect", "_SDL_SendJoystickEffect", "_SDL_SendJoystickVirtualSensorData", "_SDL_SetAppMetadata", "_SDL_SetAppMetadataProperty", "_SDL_SetAssertionHandler", "_SDL_SetAtomicInt", "_SDL_SetAtomicPointer", "_SDL_SetAtomicU32", "_SDL_SetAudioDeviceGain", "_SDL_SetAudioPostmixCallback", "_SDL_SetAudioStreamFormat", "_SDL_SetAudioStreamFrequencyRatio", "_SDL_SetAudioStreamGain", "_SDL_SetAudioStreamGetCallback", "_SDL_SetAudioStreamInputChannelMap", "_SDL_SetAudioStreamOutputChannelMap", "_SDL_SetAudioStreamPutCallback", "_SDL_SetBooleanProperty", "_SDL_SetClipboardData", "_SDL_SetClipboardText", "_SDL_SetCurrentThreadPriority", "_SDL_SetCursor", "_SDL_SetEnvironmentVariable", "_SDL_SetError", "_SDL_SetEventEnabled", "_SDL_SetEventFilter", "_SDL_SetFloatProperty", "_SDL_SetGPUBlendConstants", "_SDL_SetGPUBufferName", "_SDL_SetGPUScissor", "_SDL_SetGPUStencilReference", "_SDL_SetGPUSwapchainParameters", "_SDL_SetGPUTextureName", "_SDL_SetGPUViewport", "_SDL_SetGamepadEventsEnabled", "_SDL_SetGamepadLED", "_SDL_SetGamepadMapping", "_SDL_SetGamepadPlayerIndex", "_SDL_SetGamepadSensorEnabled", "_SDL_SetHapticAutocenter", "_SDL_SetHapticGain", "_SDL_SetHint", "_SDL_SetHintWithPriority", "_SDL_SetInitialized", "_SDL_Set<PERSON><PERSON>stickEventsEnabled", "_SDL_SetJoystickLED", "_SDL_SetJoystickPlayerIndex", "_SDL_SetJoystickVirtualAxis", "_SDL_SetJoystickVirtualBall", "_SDL_SetJoystickVirtualButton", "_SDL_SetJoystickVirtualHat", "_SDL_SetJoystickVirtualTouchpad", "_SDL_SetLinuxThreadPriority", "_SDL_SetLinuxThreadPriorityAndPolicy", "_SDL_SetLogOutputFunction", "_SDL_SetLogPriorities", "_SDL_SetLogPriority", "_SDL_SetLogPriorityPrefix", "_SDL_SetMainReady", "_SDL_SetMemoryFunctions", "_SDL_SetModState", "_SDL_SetNumberProperty", "_SDL_SetPaletteColors", "_SDL_SetPointerProperty", "_SDL_SetPointerPropertyWithCleanup", "_SDL_SetPrimarySelectionText", "_SDL_SetRenderClipRect", "_SDL_SetRenderColorScale", "_SDL_SetRenderDrawBlendMode", "_SDL_SetRenderDrawColor", "_SDL_SetRenderDrawColorFloat", "_SDL_SetRenderLogicalPresentation", "_SDL_SetRenderScale", "_SDL_SetRenderTarget", "_SDL_SetRenderVSync", "_SDL_SetRenderViewport", "_SDL_SetScancodeName", "_SDL_SetStringProperty", "_SDL_SetSurfaceAlphaMod", "_SDL_SetSurfaceBlendMode", "_SDL_SetSurfaceClipRect", "_SDL_SetSurfaceColorKey", "_SDL_SetSurfaceColorMod", "_SDL_SetSurfaceColorspace", "_SDL_SetSurfacePalette", "_SDL_SetSurfaceRLE", "_SDL_SetTLS", "_SDL_SetTextInputArea", "_SDL_SetTextureAlphaMod", "_SDL_SetTextureAlphaModFloat", "_SDL_SetTextureBlendMode", "_SDL_SetTextureColorMod", "_SDL_SetTextureColorModFloat", "_SDL_SetTextureScaleMode", "_SDL_SetWindowAlwaysOnTop", "_SDL_SetWindowAspectRatio", "_SDL_SetWindowBordered", "_SDL_SetWindowFocusable", "_SDL_SetWindowFullscreen", "_SDL_SetWindowFullscreenMode", "_SDL_SetWindowHitTest", "_SDL_SetWindowIcon", "_SDL_SetWindowKeyboardGrab", "_SDL_SetWindowMaximumSize", "_SDL_SetWindowMinimumSize", "_SDL_SetWindowModal", "_SDL_SetWindowMouseGrab", "_SDL_SetWindowMouseRect", "_SDL_SetWindowOpacity", "_SDL_SetWindowParent", "_SDL_SetWindowPosition", "_SDL_SetWindowRelativeMouseMode", "_SDL_SetWindowResizable", "_SDL_SetWindowShape", "_SDL_SetWindowSize", "_SDL_SetWindowSurfaceVSync", "_SDL_SetWindowTitle", "_SDL_SetWindowsMessageHook", "_SDL_SetX11EventHook", "_SDL_SetiOSAnimationCallback", "_SDL_SetiOSEventPump", "_SDL_ShouldInit", "_SDL_ShouldQuit", "_SDL_ShowAndroidToast", "_SDL_ShowCursor", "_SDL_ShowMessageBox", "_SDL_ShowOpenFileDialog", "_SDL_ShowOpenFolderDialog", "_SDL_ShowSaveFileDialog", "_SDL_ShowSimpleMessageBox", "_SDL_ShowWindow", "_SDL_ShowWindowSystemMenu", "_SDL_SignalCondition", "_SDL_SignalSemaphore", "_SDL_StartTextInput", "_SDL_StartTextInputWithProperties", "_SDL_StepUTF8", "_SDL_StopHapticEffect", "_SDL_StopHapticEffects", "_SDL_StopHapticRumble", "_SDL_StopTextInput", "_SDL_StorageReady", "_SDL_StringToGUID", "_SDL_SubmitGPUCommandBuffer", "_SDL_SubmitGPUCommandBufferAndAcquireFence", "_SDL_SurfaceHasAlternateImages", "_SDL_SurfaceHasColorKey", "_SDL_SurfaceHasRLE", "_SDL_SyncWindow", "_SDL_TellIO", "_SDL_TextInputActive", "_SDL_TimeFromWindows", "_SDL_TimeToDateTime", "_SDL_TimeToWindows", "_SDL_TryLockMutex", "_SDL_TryLockRWLockForReading", "_SDL_TryLockRWLockForWriting", "_SDL_TryLockSpinlock", "_SDL_TryWaitSemaphore", "_SDL_UCS4ToUTF8", "_SDL_UnbindAudioStream", "_SDL_UnbindAudioStreams", "_SDL_UnloadObject", "_SDL_UnlockAudioStream", "_SDL_UnlockJoysticks", "_SDL_UnlockMutex", "_SDL_UnlockProperties", "_SDL_UnlockRWLock", "_SDL_UnlockSpinlock", "_SDL_UnlockSurface", "_SDL_UnlockTexture", "_SDL_UnmapGPUTransferBuffer", "_SDL_UnregisterApp", "_SDL_UnsetEnvironmentVariable", "_SDL_UpdateGamepads", "_SDL_UpdateHapticEffect", "_SDL_UpdateJoysticks", "_SDL_UpdateNVTexture", "_SDL_UpdateSensors", "_SDL_UpdateTexture", "_SDL_UpdateWindowSurface", "_SDL_UpdateWindowSurfaceRects", "_SDL_UpdateYUVTexture", "_SDL_UploadToGPUBuffer", "_SDL_UploadToGPUTexture", "_SDL_Vulkan_CreateSurface", "_SDL_Vulkan_DestroySurface", "_SDL_Vulkan_GetInstanceExtensions", "_SDL_Vulkan_GetPresentationSupport", "_SDL_Vulkan_GetVkGetInstanceProcAddr", "_SDL_Vulkan_LoadLibrary", "_SDL_Vulkan_UnloadLibrary", "_SDL_WaitCondition", "_SDL_WaitConditionTimeout", "_SDL_WaitEvent", "_SDL_WaitEventTimeout", "_SDL_WaitForGPUFences", "_SDL_WaitForGPUIdle", "_SDL_WaitProcess", "_SDL_WaitSemaphore", "_SDL_WaitSemaphoreTimeout", "_SDL_WaitThread", "_SDL_WarpMouseGlobal", "_SDL_WarpMouseInWindow", "_SDL_WasInit", "_SDL_WindowHasSurface", "_SDL_WindowSupportsGPUPresentMode", "_SDL_WindowSupportsGPUSwapchainComposition", "_SDL_WriteIO", "_SDL_WriteS16BE", "_SDL_WriteS16LE", "_SDL_WriteS32BE", "_SDL_WriteS32LE", "_SDL_WriteS64BE", "_SDL_WriteS64LE", "_SDL_WriteS8", "_SDL_WriteStorageFile", "_SDL_WriteSurfacePixel", "_SDL_WriteSurfacePixelFloat", "_SDL_WriteU16BE", "_SDL_WriteU16LE", "_SDL_WriteU32BE", "_SDL_WriteU32LE", "_SDL_WriteU64BE", "_SDL_WriteU64LE", "_SDL_WriteU8", "_SDL_abs", "_SDL_acos", "_SDL_acosf", "_SDL_aligned_alloc", "_SDL_aligned_free", "_SDL_asin", "_SDL_asinf", "_SDL_asprintf", "_SDL_atan2", "_SDL_atan2f", "_SDL_atan", "_SDL_atanf", "_SDL_atof", "_SDL_atoi", "_SDL_bsearch", "_SDL_bsearch_r", "_SDL_calloc", "_SDL_ceil", "_SDL_ceilf", "_SDL_copysign", "_SDL_copysignf", "_SDL_cos", "_SDL_cosf", "_SDL_crc16", "_SDL_crc32", "_SDL_exp", "_SDL_expf", "_SDL_fabs", "_SDL_fabsf", "_SDL_floor", "_SDL_floorf", "_SDL_fmod", "_SDL_fmodf", "_SDL_free", "_SDL_getenv", "_SDL_getenv_unsafe", "_SDL_hid_ble_scan", "_SDL_hid_close", "_SDL_hid_device_change_count", "_SDL_hid_enumerate", "_SDL_hid_exit", "_SDL_hid_free_enumeration", "_SDL_hid_get_device_info", "_SDL_hid_get_feature_report", "_SDL_hid_get_indexed_string", "_SDL_hid_get_input_report", "_SDL_hid_get_manufacturer_string", "_SDL_hid_get_product_string", "_SDL_hid_get_report_descriptor", "_SDL_hid_get_serial_number_string", "_SDL_hid_init", "_SDL_hid_open", "_SDL_hid_open_path", "_SDL_hid_read", "_SDL_hid_read_timeout", "_SDL_hid_send_feature_report", "_SDL_hid_set_nonblocking", "_SDL_hid_write", "_SDL_iconv", "_SDL_iconv_close", "_SDL_iconv_open", "_SDL_iconv_string", "_SDL_isalnum", "_SDL_isalpha", "_SDL_isblank", "_SDL_iscntrl", "_SDL_isdigit", "_SDL_isgraph", "_SDL_isinf", "_SDL_isinff", "_SDL_islower", "_SDL_isnan", "_SDL_isnanf", "_SDL_isprint", "_SDL_ispunct", "_SDL_isspace", "_SDL_isupper", "_SDL_isxdigit", "_SDL_itoa", "_SDL_lltoa", "_SDL_log10", "_SDL_log10f", "_SDL_log", "_SDL_logf", "_SDL_lround", "_SDL_lroundf", "_SDL_ltoa", "_SDL_malloc", "_SDL_memcmp", "_SDL_memcpy", "_SDL_memmove", "_SDL_memset4", "_SDL_memset", "_SDL_modf", "_SDL_modff", "_SDL_murmur3_32", "_SDL_pow", "_SDL_powf", "_SDL_qsort", "_SDL_qsort_r", "_SDL_rand", "_SDL_rand_bits", "_SDL_rand_bits_r", "_SDL_rand_r", "_SDL_randf", "_SDL_randf_r", "_SDL_realloc", "_SDL_round", "_SDL_roundf", "_SDL_scalbn", "_SDL_scalbnf", "_SDL_setenv_unsafe", "_SDL_sin", "_SDL_sinf", "_SDL_snprintf", "_SDL_sqrt", "_SDL_sqrtf", "_SDL_srand", "_SDL_sscanf", "_SDL_strcasecmp", "_SDL_strcasestr", "_SDL_strchr", "_SDL_strcmp", "_SDL_strdup", "_SDL_strlcat", "_SDL_strlcpy", "_SDL_strlen", "_SDL_strlwr", "_SDL_strncasecmp", "_SDL_strncmp", "_SDL_strndup", "_SDL_strnlen", "_SDL_strnstr", "_SDL_strpbrk", "_SDL_strrchr", "_SDL_strrev", "_SDL_strstr", "_SDL_strtod", "_SDL_strtok_r", "_SDL_strtol", "_SDL_strtoll", "_SDL_strtoul", "_SDL_strtoull", "_SDL_strupr", "_SDL_swprintf", "_SDL_tan", "_SDL_tanf", "_SDL_tolower", "_SDL_toupper", "_SDL_trunc", "_SDL_truncf", "_SDL_uitoa", "_SDL_ulltoa", "_SDL_ultoa", "_SDL_unsetenv_unsafe", "_SDL_utf8strlcpy", "_SDL_utf8strlen", "_SDL_utf8strnlen", "_SDL_vasprintf", "_SDL_vsnprintf", "_SDL_vsscanf", "_SDL_vswprintf", "_SDL_wcscasecmp", "_SDL_wcscmp", "_SDL_wcsdup", "_SDL_wcslcat", "_SDL_wcslcpy", "_SDL_wcslen", "_SDL_wcsncasecmp", "_SDL_wcsncmp", "_SDL_wcsnlen", "_SDL_wcsnstr", "_SDL_wcsstr", "_SDL_wcstol", "_SDL_StepBackUTF8", "_SDL_DelayPrecise", "_SDL_CalculateGPUTextureFormatSize", "_SDL_SetErrorV", "_SDL_GetDefaultLogOutputFunction", "_SDL_RenderDebugText", "_SDL_GetSandbox", "_SDL_CancelGPUCommandBuffer", "_SDL_SaveFile_IO", "_SDL_SaveFile", "_SDL_GetCurrentDirectory", "_SDL_IsAudioDevicePhysical", "_SDL_IsAudioDevicePlayback", "_SDL_AsyncIOFromFile", "_SDL_GetAsyncIOSize", "_SDL_ReadAsyncIO", "_SDL_WriteAsyncIO", "_SDL_CloseAsyncIO", "_SDL_CreateAsyncIOQueue", "_SDL_DestroyAsyncIOQueue", "_SDL_GetAsyncIOResult", "_SDL_WaitAsyncIOResult", "_SDL_SignalAsyncIOQueue", "_SDL_LoadFileAsync", "_SDL_ShowFileDialogWithProperties", "_SDL_IsMainThread", "_SDL_RunOnMainThread", "_SDL_SetGPUAllowedFramesInFlight", "_SDL_RenderTextureAffine", "_SDL_WaitForGPUSwapchain", "_SDL_WaitAndAcquireGPUSwapchainTexture", "_SDL_RenderDebugTextFormat", "_SDL_CreateTray", "_SDL_SetTrayIcon", "_SDL_SetTrayTooltip", "_SDL_CreateTrayMenu", "_SDL_CreateTraySubmenu", "_SDL_GetTrayMenu", "_SDL_GetTraySubmenu", "_SDL_GetTrayEntries", "_SDL_RemoveTrayEntry", "_SDL_InsertTrayEntryAt", "_SDL_SetTrayEntryLabel", "_SDL_GetTrayEntryLabel", "_SDL_SetTrayEntryChecked", "_SDL_GetTrayEntryChecked", "_SDL_SetTrayEntryEnabled", "_SDL_GetTrayEntryEnabled", "_SDL_SetTrayEntryCallback", "_SDL_DestroyTray", "_SDL_GetTrayEntryParent", "_SDL_GetTrayMenuParentEntry", "_SDL_GetTrayMenuParentTray", "_SDL_GetThreadState", "_SDL_AudioStreamDevicePaused", "_SDL_ClickTrayEntry", "_SDL_UpdateTrays", "_SDL_StretchSurface"]}}], "flags": [{"attributes": ["not_app_extension_safe"]}], "install_names": [{"name": "@rpath/SDL3.framework/Versions/A/SDL3"}], "target_info": [{"min_deployment": "10.13", "target": "arm64-macos"}, {"min_deployment": "10.13", "target": "x86_64-macos"}]}, "tapi_tbd_version": 5}