#include <metal_stdlib>
#include <simd/simd.h>

using namespace metal;

struct UniformBlock
{
    float4 MultiplyColor;
};

struct main0_out
{
    float4 out_var_SV_Target0 [[color(0)]];
};

struct main0_in
{
    float2 in_var_TEXCOORD0 [[user(locn0)]];
};

fragment main0_out main0(main0_in in [[stage_in]], constant UniformBlock& _18 [[buffer(0)]], texture2d<float> Texture [[texture(0)]], sampler Sampler [[sampler(0)]])
{
    main0_out out = {};
    out.out_var_SV_Target0 = _18.MultiplyColor * Texture.sample(<PERSON><PERSON>, in.in_var_TEXCOORD0);
    return out;
}