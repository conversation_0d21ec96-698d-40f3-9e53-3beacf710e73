{"artifacts": [{"path": "sdl3_pong"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "target_link_libraries", "target_compile_options", "include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 28, "parent": 0}, {"command": 1, "file": 0, "line": 29, "parent": 0}, {"command": 2, "file": 0, "line": 39, "parent": 0}, {"command": 3, "file": 0, "line": 9, "parent": 0}, {"command": 3, "file": 0, "line": 10, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -std=gnu99 -arch arm64"}, {"backtrace": 3, "fragment": "-Wall"}, {"backtrace": 3, "fragment": "-Wextra"}, {"backtrace": 3, "fragment": "-g"}, {"backtrace": 3, "fragment": "-O0"}], "includes": [{"backtrace": 4, "path": "/Users/<USER>/Documents/try/sdlProject/SDL3Learn/sdl3Practice/lib/stb"}, {"backtrace": 5, "path": "/Users/<USER>/Documents/try/sdlProject/SDL3Learn/sdl3Practice/learn/TTF"}, {"backtrace": 2, "path": "/opt/homebrew/include"}], "language": "C", "languageStandard": {"backtraces": [1], "standard": "99"}, "sourceIndexes": [0]}], "id": "sdl3_pong::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-g", "role": "flags"}, {"fragment": "-Wl,-rpath,/opt/homebrew/lib", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/homebrew/lib/libSDL3.0.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "-lSDL3_ttf", "role": "libraries"}], "language": "C"}, "name": "sdl3_pong", "nameOnDisk": "sdl3_pong", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "learn/GPUBasic/basic7_TexturedAnimatedQuad.c", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}