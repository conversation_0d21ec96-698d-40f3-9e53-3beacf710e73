{"inputs": [{"path": "CMakeLists.txt"}, {"isGenerated": true, "path": "build/CMakeFiles/4.0.3/CMakeSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Platform/Darwin-Initialize.cmake"}, {"isGenerated": true, "path": "build/CMakeFiles/4.0.3/CMakeCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Platform/Darwin.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Platform/UnixPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/AppleClang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Platform/Apple-AppleClang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Platform/Apple-Clang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Platform/Apple-Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Linker/AppleClang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Linker/AppleClang.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Platform/Linker/Apple-AppleClang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Platform/Linker/Apple-AppleClang.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/SDL3/SDL3ConfigVersion.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/SDL3/SDL3Config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FeatureSummary.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/SDL3/SDL3headersTargets.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/SDL3/SDL3sharedTargets.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/SDL3/SDL3sharedTargets-release.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/SDL3/SDL3testTargets.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/SDL3/SDL3testTargets-release.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/SDL3_ttf/SDL3_ttfConfigVersion.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/SDL3_ttf/SDL3_ttfConfig.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/FeatureSummary.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/SDL3_ttf/SDL3_ttf-shared-targets.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/SDL3_ttf/SDL3_ttf-shared-targets-release.cmake"}], "kind": "cmakeFiles", "paths": {"build": "/Users/<USER>/Documents/try/sdlProject/SDL3Learn/sdl3Practice/build", "source": "/Users/<USER>/Documents/try/sdlProject/SDL3Learn/sdl3Practice"}, "version": {"major": 1, "minor": 1}}