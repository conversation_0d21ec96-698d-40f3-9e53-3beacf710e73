@@
@@
- TTF_GlyphIsProvided32
+ TTF_GlyphIsProvided
  (...)
@@
@@
- TTF_GlyphMetrics32
+ TTF_GlyphMetrics
  (...)
@@
@@
- TTF_SizeText
+ TTF_GetStringSize
  (...)
@@
@@
- TTF_SizeUTF8
+ TTF_GetStringSize
  (...)
@@
@@
- TTF_MeasureUTF8
+ TTF_MeasureString
  (...)
@@
@@
- TTF_RenderUTF8_Solid
+ SDL_RenderText_Solid
  (...)
@@
@@
- TTF_RenderUTF8_Solid_Wrapped
+ TTF_RenderText_Solid_Wrapped
  (...)
@@
@@
- TTF_RenderGlyph32_Solid
+ TTF_RenderGlyph_Solid
  (...)
@@
@@
- TTF_RenderGlyph32_Solid
+ TTF_RenderGlyph_Solid
  (...)
@@
@@
- TTF_RenderUTF8_Shaded
+ TTF_RenderText_Shaded
  (...)
@@
@@
- TTF_RenderUTF8_Shaded_Wrapped
+ TTF_RenderText_Shaded_Wrapped
  (...)
@@
@@
- TTF_RenderGlyph32_Shaded
+ TTF_RenderGlyph_Shaded
  (...)
@@
@@
- TTF_RenderUTF8_Blended
+ TTF_RenderText_Blended
  (...)
@@
@@
- TTF_RenderUTF8_Blended_Wrapped
+ TTF_RenderText_Blended_Wrapped
  (...)
@@
@@
- TTF_RenderGlyph32_Blended
+ TTF_RenderGlyph_Blended
  (...)
@@
@@
- TTF_RenderUTF8_LCD
+ TTF_RenderText_LCD
  (...)
@@
@@
- TTF_RenderUTF8_LCD_Wrapped
+ TTF_RenderText_LCD_Wrapped
  (...)
@@
@@
- TTF_RenderGlyph32_LCD
+ TTF_RenderGlyph_LCD
  (...)
@@
@@
- TTF_WRAPPED_ALIGN_LEFT
+ TTF_HORIZONTAL_ALIGN_LEFT
@@
@@
- TTF_WRAPPED_ALIGN_CENTER
+ TTF_HORIZONTAL_ALIGN_CENTER
@@
@@
- TTF_WRAPPED_ALIGN_RIGHT
+ TTF_HORIZONTAL_ALIGN_RIGHT
@@
@@
- TTF_GetFontWrappedAlign
+ TTF_GetFontWrapAlignment
  (...)
@@
@@
- TTF_SetFontWrappedAlign
+ TTF_SetFontWrapAlignment
  (...)
@@
@@
- TTF_FontHeight
+ TTF_GetFontHeight
  (...)
@@
@@
- TTF_FontAscent
+ TTF_GetFontAscent
  (...)
@@
@@
- TTF_FontDescent
+ TTF_GetFontDescent
  (...)
@@
@@
- TTF_FontLineSkip
+ TTF_GetFontLineSkip
  (...)
@@
@@
- TTF_FontFaces
+ TTF_GetNumFontFaces
  (...)
@@
@@
- TTF_FontFaceIsFixedWidth
+ TTF_FontIsFixedWidth
  (...)
@@
@@
- TTF_FontFaceFamilyName
+ TTF_GetFontFamilyName
  (...)
@@
@@
- TTF_FontFaceStyleName
+ TTF_GetFontStyleName
  (...)
@@
@@
- TTF_GlyphIsProvided
+ TTF_FontHasGlyph
  (...)
@@
@@
- TTF_GlyphMetrics
+ TTF_GetGlyphMetrics
  (...)
@@
@@
- TTF_IsFontScalable
+ TTF_FontIsScalable
  (...)
@@
@@
- TTF_SetFontScriptName
+ TTF_SetFontScript
  (...)
