{"name": "SDL3_ttf", "remote": "libsdl-org/SDL_ttf", "dependencies": {"SDL": {"startswith": "3.", "repo": "libsdl-org/SDL"}}, "version": {"file": "include/SDL3_ttf/SDL_ttf.h", "re_major": "^#define SDL_TTF_MAJOR_VERSION\\s+([0-9]+)$", "re_minor": "^#define SDL_TTF_MINOR_VERSION\\s+([0-9]+)$", "re_micro": "^#define SDL_TTF_MICRO_VERSION\\s+([0-9]+)$"}, "source": {"checks": ["src/SDL_ttf.c", "include/SDL3_ttf/SDL_textengine.h", "include/SDL3_ttf/SDL_ttf.h", "src/SDL_surface_textengine.c"]}, "dmg": {"project": "Xcode/SDL_ttf.xcodeproj", "path": "Xcode/build/SDL3_ttf.dmg", "scheme": "SDL3_ttf.dmg", "dependencies": {"SDL": {"artifact": "SDL3-*.dmg"}}}, "mingw": {"cmake": {"archs": ["x86", "x64"], "args": ["-DBUILD_SHARED_LIBS=ON", "-DSDLTTF_HARFBUZZ=ON", "-DSDLTTF_SAMPLES=OFF", "-DSDLTTF_VENDORED=ON"], "shared-static": "args"}, "files": {"": ["build-scripts/pkg-support/mingw/INSTALL.md.in:INSTALL.md", "build-scripts/pkg-support/mingw/Makefile", "LICENSE.txt", "README.md"], "cmake": ["build-scripts/pkg-support/mingw/cmake/SDL3_ttfConfig.cmake", "build-scripts/pkg-support/mingw/cmake/SDL3_ttfConfigVersion.cmake"]}, "dependencies": {"SDL": {"artifact": "SDL3-devel-*-mingw.tar.gz", "install-command": "make install-@<@ARCH@>@ DESTDIR=@<@PREFIX@>@"}}}, "msvc": {"msbuild": {"archs": ["x86", "x64"], "projects": ["VisualC/SDL_ttf.vcxproj"], "prebuilt": ["VisualC/external/lib/@<@ARCH@>@/*"], "files-lib": {"": ["VisualC/@<@PLATFORM@>@/@<@CONFIGURATION@>@/SDL3_ttf.dll"]}, "files-devel": {"lib/@<@ARCH@>@": ["VisualC/@<@PLATFORM@>@/@<@CONFIGURATION@>@/SDL3_ttf.dll", "VisualC/@<@PLATFORM@>@/@<@CONFIGURATION@>@/SDL3_ttf.lib", "VisualC/@<@PLATFORM@>@/@<@CONFIGURATION@>@/SDL3_ttf.pdb"]}}, "cmake": {"archs": ["arm64"], "args": ["-DSDLTTF_HARFBUZZ=ON", "-DSDLTTF_SAMPLES=OFF", "-DSDLTTF_VENDORED=ON"], "files-lib": {"": ["bin/SDL3_ttf.dll"]}, "files-devel": {"lib/@<@ARCH@>@": ["bin/SDL3_ttf.dll", "bin/SDL3_ttf.pdb", "lib/SDL3_ttf.lib"]}}, "files-lib": {"": ["build-scripts/pkg-support/msvc/@<@ARCH@>@/INSTALL.md.in:INSTALL.md", "LICENSE.txt", "README.md"]}, "files-devel": {"": ["build-scripts/pkg-support/msvc/INSTALL.md.in:INSTALL.md", "LICENSE.txt", "README.md"], "cmake": ["build-scripts/pkg-support/msvc/cmake/SDL3_ttfConfig.cmake.in:SDL3_ttfConfig.cmake", "build-scripts/pkg-support/msvc/cmake/SDL3_ttfConfigVersion.cmake.in:SDL3_ttfConfigVersion.cmake", "cmake/sdlcpu.cmake"], "include/SDL3_ttf": ["include/SDL3_ttf/*.h"]}, "dependencies": {"SDL": {"artifact": "SDL3-devel-*-VC.zip", "copy": [{"src": "lib/@<@ARCH@>@/SDL3.*", "dst": "../SDL/VisualC/@<@PLATFORM@>@/@<@CONFIGURATION@>@"}, {"src": "include/SDL3/*", "dst": "../SDL/include/SDL3"}]}}}, "android": {"cmake": {"args": ["-DSDLTTF_HARFBUZZ=ON", "-DSDLTTF_SAMPLES=OFF", "-DSDLTTF_VENDORED=ON"]}, "modules": {"SDL3_ttf-shared": {"type": "library", "library": "lib/libSDL3_ttf.so", "includes": {"SDL3_ttf": ["include/SDL3_ttf/*.h"]}}, "SDL3_ttf": {"type": "interface", "export-libraries": [":SDL3_ttf-shared"]}}, "abis": ["armeabi-v7a", "arm64-v8a", "x86", "x86_64"], "api-minimum": 19, "api-target": 29, "ndk-minimum": 21, "aar-files": {"": ["build-scripts/pkg-support/android/aar/__main__.py.in:__main__.py", "build-scripts/pkg-support/android/aar/description.json.in:description.json"], "META-INF": ["LICENSE.txt"], "cmake": ["cmake/sdlcpu.cmake", "build-scripts/pkg-support/android/aar/cmake/SDL3_ttfConfig.cmake", "build-scripts/pkg-support/android/aar/cmake/SDL3_ttfConfigVersion.cmake.in:SDL3_ttfConfigVersion.cmake"]}, "files": {"": ["build-scripts/pkg-support/android/INSTALL.md.in:INSTALL.md", "LICENSE.txt", "README.md"]}, "dependencies": {"SDL": {"artifact": "SDL3-devel-*-android.zip"}}}}